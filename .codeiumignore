# .codeiumignore — 控制 Windsurf / Cascade 可访问与可编辑的范围
# 语法与 .gitignore 相同。被忽略的路径将：
# 1) 不被索引（不计入索引文件配额）；
# 2) 不被 Cascade 查看、编辑或创建；
# 3) 主要用于排除大型二进制/第三方产物/构建输出，尽量不屏蔽业务源码。
# 规则来源：.gitignore 与 .cursorignore；仅保留“必要忽略”。

##############################
# 1) 操作系统与编辑器缓存/临时文件
##############################
.DS_Store
.idea/

##############################
# 2) 构建产物与中间文件（无需 AI 介入）
##############################
/build/
DerivedData/
QCYZT/build/
QCYZT/build/XCBuildData/
xcuserdata
profile
*.moved-aside
*.hmap
*.xccheckout
*.xcuserstate
xcschememanagement.plist

##############################
# 3) Xcode 工程/工作区中不建议由 AI 修改的文件
#    如需临时允许，可在会话中说明并注释掉对应规则
##############################
*.xcworkspace
*.xcodeproj/xcuserdata/
**/*.pbxproj

##############################
# 4) 包管理与第三方依赖（体量大、无需 AI 编辑）
##############################
Pods/

##############################
# 5) 第三方二进制与资源聚合目录（来自 .cursorignore）
#    通常无需 AI 编辑，且体量较大
##############################
TencentCloudHuiyanSDKFace_framework/
ThirdFrameworks/
DependencyFrameworks/

##############################
# 6) 大型二进制/打包产物与资源包
##############################
*.a
*.framework
*.bundle
*.ipa
*.app
*.dSYM

##############################
# 7) 自动生成文件（通常无需手改）
##############################
*.generated.*
**/Generated/
# 可能包含 protobuf/其他工具生成的中间文件
*.pb.*

##############################
# 8) 日志与临时文件
##############################
*.log
*.bak
*.swp
*~.nib
*.mode*
*.perspective*

##############################
# 9) 可选（按需开启）：更严格的资源排除
#    如果你希望进一步减少索引体量，可放开以下规则
#    注意：开启后 Cascade 将无法查看这些资源文件
##############################
 *.png
 *.jpg
 *.jpeg
 *.gif
 *.mp3
 *.mp4
 *.mov
 *.wav
 *.aiff
 *.pdf
