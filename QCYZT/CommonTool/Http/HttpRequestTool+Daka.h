//
//  HttpRequestTool+Daka.h
//  QCYZT
//
//  Created by shum<PERSON> on 2022/8/31.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "HttpRequestTool.h"

NS_ASSUME_NONNULL_BEGIN

@interface HttpRequestTool (Daka)

// 关注投顾列表
+ (void)requestfocusDakaListWithStart:(void (^)())startBlock
                    failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success;

// 关注投顾
+ (void)focusDakaWithUserId:(NSString *)userid
                     roomId:(NSString *)roomId
                      start:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success;

// 取消关注
+ (void)cancelFocusDakaWithUserId:(NSString *)userid
                            start:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success;

// 投顾列表
+(void)getDakaListWithWithPage:(NSInteger)page
                      pageSize:(NSInteger)pageSize
                     orderType:(NSString *)orderType
                      userType:(NSString *)userType
                       keyWord:(NSString *)keyWord
                        goodAt:(NSString *)goodAt
                      listType:(NSString *)listType
                         start:(void (^)())startBlock
                       failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success;

// 投顾详情
+ (void)getDakaDetailWithUserId:(NSString *)userid
                          start:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success;

// 投顾banner
+ (void)getDakaBannerWithUserId:(NSString *)userid
                          start:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success;

/// 获取投顾笔记列表
+ (void)getDakaNoteListWithDakaId:(NSString *)dakaId
                         onlyNote:(BOOL)onlyNote
                             sign:(NSString *)sign
                             Page:(NSInteger)page
                         pageSize:(NSInteger)pageSize
                            start:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success;

// 投顾神操作
+ (void)getDakaOperationListWithStart:(void (^)())startBlock
                        failure:(void (^)())failBlock
                              success:(requestSuccessBlock)success;

// 月度教学汇总列表
+ (void)getMonthlyTeachingSummaryWithTimeRange:(NSString *)timeRange
                                      sortType:(NSString *)sortType
                                     advisorId:(NSString * _Nullable)advisorId
                                         start:(void (^)())startBlock
                                       failure:(void (^)())failBlock
                                       success:(requestSuccessBlock)success;

@end

NS_ASSUME_NONNULL_END
