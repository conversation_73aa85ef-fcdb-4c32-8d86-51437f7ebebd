//
//  HttpRequestTool+Daka.m
//  QCYZT
//
//  Created by shum<PERSON> on 2022/8/31.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "HttpRequestTool+Daka.h"

#define kAPI_Bigname_FocusList   KDakaBaseUrl(@"/api/v2/bigname/noticedList")        // 关注投顾列表
#define kAPI_Bigname_Focus       KDakaBaseUrl(@"/api/v2/bigname/notice.do")        // 关注投顾
#define kAPI_Bigname_CancelFocus KDakaBaseUrl(@"/api/v2/bigname/cancel.do")        // 取消关注投顾
#define kAPI_Bigname_DakaList    KDakaBaseUrl(@"/api/v2/bigname/list.do")          // 投顾列表
#define kAPI_Bigname_Detail      KDakaBaseUrl(@"/api/v2/bigname/detail.do")        // 投顾详情
#define kAPI_Bigname_Banner      KDakaBaseUrl(@"/api/v2/personalBanner")        // 个人中心banner
#define kAPI_Bigname_NoteList    KDakaBaseUrl(@"/api/v2/note/dkNoteList")       // 投顾笔记列表
#define kAPI_Bigname_FollowNoteList    KDakaBaseUrl(@"/api/v2/note/dkFollowingNoteList")       // 投顾跟踪笔记列表
#define kAPI_Bigname_VideoNoteList    KDakaBaseUrl(@"/api/v2/note/dkVideoNoteList")       // 投顾短视频笔记列表

#define kAPI_Bigname_MyNoteList    KDakaBaseUrl(@"/api/v2/note/writeNoteList")       // 投顾笔记列表
#define kAPI_Bigname_OperationList    KDakaBaseUrl(@"/api/v2/divineStock/list")       // 投顾神操作列表
#define kAPI_Bigname_MonthlyTeachingSummary    KDakaBaseUrl(@"/api/v2/monthlyTeaching/summary")       // 月度教学汇总列表

@implementation HttpRequestTool (Daka)

// 关注投顾列表
+ (void)requestfocusDakaListWithStart:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success {
    [self getDataInfoWithUrl:kAPI_Bigname_FocusList
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 关注投顾
+ (void)focusDakaWithUserId:(NSString *)userid
                     roomId:(NSString *)roomId
                      start:(void (^)())startBlock
                    failure:(void (^)())failBlock
                    success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (userid) {
        [params setObject:userid forKey:@"userid"];
    }
    if (roomId.length >0) {
        [params setObject:roomId forKey:@"roomId"];
    }
    [self getDataInfoWithUrl:kAPI_Bigname_Focus
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 取消关注
+ (void)cancelFocusDakaWithUserId:(NSString *)userid
                            start:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (userid) {
        [params setObject:userid forKey:@"userid"];
    }
    
    [self getDataInfoWithUrl:kAPI_Bigname_CancelFocus
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 投顾列表
+(void)getDakaListWithWithPage:(NSInteger)page
                      pageSize:(NSInteger)pageSize
                     orderType:(NSString *)orderType
                      userType:(NSString *)userType
                       keyWord:(NSString *)keyWord
                        goodAt:(NSString *)goodAt
                      listType:(NSString *)listType
                         start:(void (^)())startBlock
                       failure:(void (^)())failBlock
                       success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[NSString stringWithFormat:@"%zd", page] forKey:@"page_no"];
    [params setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"page_size"];
    if (orderType) {
        [params setObject:orderType forKey:@"order_type"];
    }
    if (listType) {
        [params setObject:listType forKey:@"list_type"];
    }
    if (userType) {
        [params setObject:userType forKey:@"user_type"];
    }
    if (keyWord) {
        [params setObject:keyWord forKey:@"key_word"];
    }
    if (goodAt) {
        [params setObject:goodAt forKey:@"good_at"];
    }
    [self getDataInfoWithUrl:kAPI_Bigname_DakaList
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 投顾详情
+ (void)getDakaDetailWithUserId:(NSString *)userid
                          start:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (userid) {
        [params setObject:userid forKey:@"userid"];
    }
    [self postDataJsonInfoWithUrl:kAPI_Bigname_Detail params:params start:startBlock failure:failBlock success:success];
}

// 投顾banner
+ (void)getDakaBannerWithUserId:(NSString *)userid
                          start:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success{
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    if (userid) {
        [params setObject:userid forKey:@"bignameId"];
    }
    
    [self getDataInfoWithUrl:kAPI_Bigname_Banner
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

/// 获取投顾笔记列表
+ (void)getDakaNoteListWithDakaId:(NSString *)dakaId
                         onlyNote:(BOOL)onlyNote
                             sign:(NSString *)sign
                             Page:(NSInteger)page
                         pageSize:(NSInteger)pageSize
                            start:(void (^)())startBlock
                          failure:(void (^)())failBlock
                          success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    [params setObject:[NSString stringWithFormat:@"%zd", page] forKey:@"pageNo"];
    [params setObject:[NSString stringWithFormat:@"%zd", pageSize] forKey:@"pageSize"];
    if (dakaId.length > 0) {
        [params setObject:dakaId forKey:@"noteAuthorid"];
    }
    if (onlyNote) {
        [params setObject:@"1" forKey:@"onlyNote"];
    }
    if ([sign isEqualToString:@"跟踪"]) {
        [self postDataJsonInfoWithUrl:kAPI_Bigname_FollowNoteList params:params start:startBlock failure:failBlock success:success];
    } else if ([sign isEqualToString:@"短视频"]) {
        [self postDataJsonInfoWithUrl:kAPI_Bigname_VideoNoteList params:params start:startBlock failure:failBlock success:success];
    } else {
        sign = [sign isEqualToString:@"全部"] ? @"" : sign;
        if (sign.length > 0) {
            [params setObject:sign forKey:@"sign"];
        }
        if ([dakaId isEqualToString:[FMUserDefault getUserId]]) {
            [self postDataJsonInfoWithUrl:kAPI_Bigname_MyNoteList params:params start:startBlock failure:failBlock success:success];
        } else {
            [self postDataJsonInfoWithUrl:kAPI_Bigname_NoteList params:params start:startBlock failure:failBlock success:success];
        }
    }
}

// 投顾神操作
+ (void)getDakaOperationListWithStart:(void (^)())startBlock
                        failure:(void (^)())failBlock
                        success:(requestSuccessBlock)success{
    [self getDataInfoWithUrl:kAPI_Bigname_OperationList
                      params:nil
                       start:startBlock
                     failure:failBlock
                     success:success];
}

// 月度教学汇总列表
+ (void)getMonthlyTeachingSummaryWithTimeRange:(NSString *)timeRange
                                      sortType:(NSString *)sortType
                                     advisorId:(NSString * _Nullable)advisorId
                                         start:(void (^)())startBlock
                                       failure:(void (^)())failBlock
                                       success:(requestSuccessBlock)success {
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    
    if (timeRange.length) {
        [params setObject:timeRange forKey:@"timeRange"];
    }
    
    if (sortType.length) {
        [params setObject:sortType forKey:@"sortType"];
    }
    
    if (advisorId.length) {
        [params setObject:advisorId forKey:@"advisorId"];
    }
    
    [self getDataInfoWithUrl:kAPI_Bigname_MonthlyTeachingSummary
                      params:params
                       start:startBlock
                     failure:failBlock
                     success:success];
}

@end
