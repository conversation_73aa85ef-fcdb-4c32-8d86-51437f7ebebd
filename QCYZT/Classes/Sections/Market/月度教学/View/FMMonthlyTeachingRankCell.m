//
//  FMMonthlyTeachingRankCell.m
//  QCYZT
//
//  Created by Augment on 2025-09-01
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMMonthlyTeachingRankCell.h"
#import "FMTeachingRecordTableView.h"

@interface FMMonthlyTeachingRankCell()

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIView *containerView;

// 排名相关
@property (nonatomic, strong) UIImageView *rankImgV;
@property (nonatomic, strong) ZLTagLabel *rankLabel;

// 投顾信息
@property (nonatomic, strong) UIView *iconBgView;
@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) UIButton *followBtn;

// 统计数据
@property (nonatomic, strong) UILabel *averageIncreaseLabel;
@property (nonatomic, strong) UILabel *averageIncreaseDescLabel;
@property (nonatomic, strong) UILabel *upRateLabel;
@property (nonatomic, strong) UILabel *upRateDescLabel;
@property (nonatomic, strong) UILabel *maxIncreaseLabel;
@property (nonatomic, strong) UILabel *maxIncreaseDescLabel;
@property (nonatomic, strong) UILabel *teachingCountLabel;
@property (nonatomic, strong) UILabel *teachingCountDescLabel;

// 展开收起
@property (nonatomic, strong) UIButton *expandBtn;
@property (nonatomic, strong) UILabel *expandLabel;

// 教学记录表格
@property (nonatomic, strong) FMTeachingRecordTableView *recordTableView;

@end

@implementation FMMonthlyTeachingRankCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.contentView.backgroundColor = ColorWithHex(0xf5f5f5);
    
    [self.contentView addSubview:self.bgView];
    [self.bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(10);
        make.bottom.equalTo(-5);
    }];
    
    [self.bgView addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(12);
        make.right.equalTo(-12);
        make.top.equalTo(12);
        make.bottom.equalTo(-12);
    }];
    
    [self setupRankViews];
    [self setupTeacherInfoViews];
    [self setupStatisticsViews];
    [self setupExpandViews];
    [self setupRecordTableView];
}

- (void)setupRankViews {
    // 排名图标（2、3名）
    [self.bgView addSubview:self.rankImgV];
    [self.rankImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.containerView).offset(-9);
        make.width.equalTo(33);
        make.height.equalTo(32);
    }];
    self.rankImgV.hidden = YES;
    
    // 排名标签（4名及以后）
    [self.containerView addSubview:self.rankLabel];
    [self.rankLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(0);
        make.height.equalTo(20);
    }];
    self.rankLabel.hidden = YES;
}

- (void)setupTeacherInfoViews {
    // 头像背景
    [self.containerView addSubview:self.iconBgView];
    [self.iconBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(16);
        make.width.height.equalTo(40);
    }];
    
    // 头像
    [self.iconBgView addSubview:self.iconImgV];
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(0);
        make.width.height.equalTo(37);
    }];
    
    // 姓名和标签
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:5 arrangedSubviews:@[self.nameLabel, self.tagView]];
    [self.containerView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconBgView.mas_right).offset(7.5);
        make.centerY.equalTo(self.iconBgView);
        make.right.equalTo(-115);
    }];
    
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(17);
    }];
    
    // 追随按钮
    [self.containerView addSubview:self.followBtn];
    [self.followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(self.iconBgView);
        make.width.equalTo(87);
        make.height.equalTo(33);
    }];
}

- (void)setupStatisticsViews {
    // 统计数据容器
    UIView *statisticsView = [[UIView alloc] init];
    [self.containerView addSubview:statisticsView];
    [statisticsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(self.iconBgView.mas_bottom).offset(20);
        make.height.equalTo(60);
    }];
    
    // 创建4个统计项
    NSArray *dataLabels = @[self.averageIncreaseLabel, self.upRateLabel, self.maxIncreaseLabel, self.teachingCountLabel];
    NSArray *descLabels = @[self.averageIncreaseDescLabel, self.upRateDescLabel, self.maxIncreaseDescLabel, self.teachingCountDescLabel];
    
    for (int i = 0; i < 4; i++) {
        UIView *itemView = [[UIView alloc] init];
        [statisticsView addSubview:itemView];
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.equalTo(0);
            make.width.equalTo(statisticsView).multipliedBy(0.25);
            make.left.equalTo(statisticsView).offset(i * (UI_SCREEN_WIDTH - 60) / 4);
        }];
        
        UILabel *dataLabel = dataLabels[i];
        UILabel *descLabel = descLabels[i];
        
        [itemView addSubview:dataLabel];
        [dataLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.top.equalTo(5);
        }];
        
        [itemView addSubview:descLabel];
        [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.bottom.equalTo(-5);
        }];
    }
}

- (void)setupExpandViews {
    // 展开收起按钮
    [self.containerView addSubview:self.expandBtn];
    [self.expandBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(self.averageIncreaseDescLabel.mas_bottom).offset(15);
        make.width.equalTo(80);
        make.height.equalTo(30);
    }];
    
    // 展开收起文字
    [self.containerView addSubview:self.expandLabel];
    [self.expandLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(0);
        make.top.equalTo(self.expandBtn.mas_bottom).offset(5);
    }];
}

- (void)setupRecordTableView {
    // 教学记录表格
    [self.containerView addSubview:self.recordTableView];
    [self.recordTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.top.equalTo(self.expandLabel.mas_bottom).offset(10);
        make.bottom.equalTo(0);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 设置圆角
    if (self.isLastCell) {
        [self.bgView layerAndBezierPathWithRect:self.bgView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerBottomLeft|UIRectCornerBottomRight];
    } else {
        [self.bgView layerAndBezierPathWithRect:self.bgView.bounds cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerAllCorners];
    }
    
    // 根据排名设置背景色
    if (self.model.rank == 1) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfff4e6), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else if (self.model.rank == 2) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xe5f3ff), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else if (self.model.rank == 3) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffeadb), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else {
        self.containerView.backgroundColor = FMWhiteColor;
    }
    
    // 设置标签背景色
    for (ZLTagLabel *tagLabel in self.tagView.subviews) {
        tagLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfeddb5), ColorWithHex(0xfdc48d)] withFrame:tagLabel.bounds direction:GradientDirectionLeftToRight];
    }
}

- (void)setModel:(FMMonthlyTeachingSummaryModel *)model {
    _model = model;
    
    [self updateRankDisplay];
    [self updateTeacherInfo];
    [self updateStatistics];
    [self updateExpandState];
    [self updateRecordTable];
}

- (void)updateRankDisplay {
    if (self.model.rank == 1) {
        // 第一名显示金色皇冠
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"rank_crown_gold");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xFFD700), ColorWithHex(0xFFA500)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else if (self.model.rank == 2) {
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"daka_operation_second");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xD2E1EE), ColorWithHex(0x8DAFCA)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else if (self.model.rank == 3) {
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"daka_operation_third");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffe7d4), ColorWithHex(0xffc196)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else {
        self.rankImgV.hidden = YES;
        self.rankLabel.hidden = NO;
        self.rankLabel.text = [NSString stringWithFormat:@"%zd", self.model.rank];
        self.iconBgView.backgroundColor = ColorWithHex(0xF2EBEB);
    }
}

- (void)updateTeacherInfo {
    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:self.model.teacherInfo.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = self.model.teacherInfo.userName;
    
    if (self.model.teacherInfo.userGoodAt.length) {
        NSArray *goodAts = [self.model.teacherInfo.userGoodAt componentsSeparatedByString:@"、"];
        self.tagView.titleArray = goodAts;
        self.tagView.hidden = NO;
    } else {
        self.tagView.hidden = YES;
    }
}

- (void)updateStatistics {
    // 平均涨幅
    CGFloat avgIncrease = [self.model.averageIncrease floatValue];
    self.averageIncreaseLabel.text = [NSString stringWithFormat:@"%@%.2f%%", avgIncrease >= 0 ? @"+" : @"", avgIncrease];
    self.averageIncreaseLabel.textColor = avgIncrease >= 0 ? ColorWithHex(0xfc0002) : ColorWithHex(0x00aa00);
    
    // 上涨率
    self.upRateLabel.text = [NSString stringWithFormat:@"%@%%", self.model.upRate];
    self.upRateLabel.textColor = ColorWithHex(0xfc0002);
    
    // 最高涨幅
    CGFloat maxIncrease = [self.model.maxIncrease floatValue];
    self.maxIncreaseLabel.text = [NSString stringWithFormat:@"%@%.2f%%", maxIncrease >= 0 ? @"+" : @"", maxIncrease];
    self.maxIncreaseLabel.textColor = maxIncrease >= 0 ? ColorWithHex(0xfc0002) : ColorWithHex(0x00aa00);
    
    // 教学数
    self.teachingCountLabel.text = [NSString stringWithFormat:@"%zd", self.model.teachingCount];
    self.teachingCountLabel.textColor = FMZeroColor;
}

- (void)updateExpandState {
    if (self.model.isExpanded) {
        self.expandLabel.text = @"收起教学 ⬆️";
        [self.expandBtn setImage:ImageWithName(@"arrow_up") forState:UIControlStateNormal];
    } else {
        self.expandLabel.text = @"展开教学 ⬇️";
        [self.expandBtn setImage:ImageWithName(@"arrow_down") forState:UIControlStateNormal];
    }
}

- (void)updateRecordTable {
    self.recordTableView.model = self.model;
    [self.recordTableView reloadData];
}

#pragma mark - Actions
- (void)followBtnClicked {
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@&secondLevel=2", self.model.teacherInfo.userId]];
}

- (void)expandBtnClicked {
    BOOL newExpandState = !self.model.isExpanded;
    if (self.expandToggleBlock) {
        self.expandToggleBlock(newExpandState);
    }
}

#pragma mark - Getter
- (UIView *)bgView {
    if (!_bgView) {
        _bgView = [UIView new];
        _bgView.backgroundColor = ColorWithHex(0xf4a483);
    }
    return _bgView;
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [UIView new];
        UI_View_Radius(_containerView, 10);
    }
    return _containerView;
}

- (UIImageView *)rankImgV {
    if (!_rankImgV) {
        _rankImgV = [UIImageView new];
    }
    return _rankImgV;
}

- (ZLTagLabel *)rankLabel {
    if (!_rankLabel) {
        _rankLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x8b6666) backgroundColor:ColorWithHex(0xf2ebeb) numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _rankLabel.widthPadding = 13;
    }
    return _rankLabel;
}

- (UIView *)iconBgView {
    if (!_iconBgView) {
        _iconBgView = [UIView new];
        UI_View_Radius(_iconBgView, 20);
    }
    return _iconBgView;
}

- (UIImageView *)iconImgV {
    if (!_iconImgV) {
        _iconImgV = [UIImageView new];
        UI_View_Radius(_iconImgV, 18.5);
    }
    return _iconImgV;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    return _nameLabel;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] init];
        _tagView.tagLabelFont = FontWithSize(11);
        _tagView.tagLabelWidthPadding = 8.0f;
        _tagView.tagLabelHeightPadding = 4.0f;
        _tagView.middlePadding = 5;
        _tagView.tagLabelTextColor = ColorWithHex(0xa56112);
        _tagView.tagLabelCornerRadius = 2.0f;
        _tagView.numberofLines = 1;
        _tagView.tagLabelBgColor = ColorWithHex(0xfeddb5);
    }
    return _tagView;
}

- (UIButton *)followBtn {
    if (!_followBtn) {
        _followBtn = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(14) normalTextColor:FMWhiteColor backgroundColor:[UIColor lz_gradientColors:@[ColorWithHex(0xfc5219), ColorWithHex(0xfc0002)] withFrame:CGRectMake(0, 0, 87, 33) direction:GradientDirectionLeftToRight] title:@"追随老师" image:nil target:self action:@selector(followBtnClicked)];
        UI_View_Radius(_followBtn, 5.0f);
    }
    return _followBtn;
}

- (UILabel *)averageIncreaseLabel {
    if (!_averageIncreaseLabel) {
        _averageIncreaseLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _averageIncreaseLabel;
}

- (UILabel *)averageIncreaseDescLabel {
    if (!_averageIncreaseDescLabel) {
        _averageIncreaseDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _averageIncreaseDescLabel.text = @"平均涨幅";
    }
    return _averageIncreaseDescLabel;
}

- (UILabel *)upRateLabel {
    if (!_upRateLabel) {
        _upRateLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _upRateLabel;
}

- (UILabel *)upRateDescLabel {
    if (!_upRateDescLabel) {
        _upRateDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _upRateDescLabel.text = @"上涨率";
    }
    return _upRateDescLabel;
}

- (UILabel *)maxIncreaseLabel {
    if (!_maxIncreaseLabel) {
        _maxIncreaseLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _maxIncreaseLabel;
}

- (UILabel *)maxIncreaseDescLabel {
    if (!_maxIncreaseDescLabel) {
        _maxIncreaseDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _maxIncreaseDescLabel.text = @"最高涨幅";
    }
    return _maxIncreaseDescLabel;
}

- (UILabel *)teachingCountLabel {
    if (!_teachingCountLabel) {
        _teachingCountLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _teachingCountLabel;
}

- (UILabel *)teachingCountDescLabel {
    if (!_teachingCountDescLabel) {
        _teachingCountDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _teachingCountDescLabel.text = @"教学数";
    }
    return _teachingCountDescLabel;
}

- (UIButton *)expandBtn {
    if (!_expandBtn) {
        _expandBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"arrow_down") target:self action:@selector(expandBtnClicked)];
    }
    return _expandBtn;
}

- (UILabel *)expandLabel {
    if (!_expandLabel) {
        _expandLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _expandLabel.text = @"展开教学 ⬇️";
    }
    return _expandLabel;
}

- (FMTeachingRecordTableView *)recordTableView {
    if (!_recordTableView) {
        _recordTableView = [[FMTeachingRecordTableView alloc] init];
    }
    return _recordTableView;
}

@end
