//
//  FMMonthlyTeachingRankCell.m
//  QCYZT
//
//  Created by Windsurf on 2025-09-01
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMMonthlyTeachingRankCell.h"
#import "FMTeachingRecordTableView.h"

@interface FMMonthlyTeachingRankCell()

@property (nonatomic, strong) UIView *containerView;

// 排名相关
@property (nonatomic, strong) UIImageView *rankImgV;
@property (nonatomic, strong) ZLTagLabel *rankLabel;

// 投顾信息
@property (nonatomic, strong) UIImageView *iconImgV;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) ZLTagView *tagView;
@property (nonatomic, strong) UIButton *followBtn1; // 第一名用的追随按钮
@property (nonatomic, strong) UIButton *followBtn2; // 其它人用的追随按钮

// 统计数据
@property (nonatomic, strong) UILabel *averageIncreaseLabel;
@property (nonatomic, strong) UILabel *averageIncreaseDescLabel;
@property (nonatomic, strong) UILabel *upRateLabel;
@property (nonatomic, strong) UILabel *upRateDescLabel;
@property (nonatomic, strong) UILabel *maxIncreaseLabel;
@property (nonatomic, strong) UILabel *maxIncreaseDescLabel;
@property (nonatomic, strong) UILabel *teachingCountLabel;
@property (nonatomic, strong) UILabel *teachingCountDescLabel;

// 展开收起
@property (nonatomic, strong) UIButton *expandBtn;

// 教学记录表格
@property (nonatomic, strong) FMTeachingRecordTableView *recordTableView;

@end

@implementation FMMonthlyTeachingRankCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.contentView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    
    [self.contentView addSubview:self.containerView];
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.top.equalTo(0);
        make.bottom.equalTo(-15);
    }];
    
    [self setupRankViews];
    [self setupTeacherInfoViews];
    [self setupStatisticsViews];
    [self setupExpandViews];
}

- (void)setupRankViews {
    // 1-3名皇冠图标
    [self.contentView addSubview:self.rankImgV];
    [self.rankImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(10);
        make.top.equalTo(-5);
    }];
    self.rankImgV.hidden = YES;
    
    // 4名及以后排名数字
    [self.containerView addSubview:self.rankLabel];
    [self.rankLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(0);
        make.height.equalTo(20);
    }];
    self.rankLabel.hidden = YES;
}

- (void)setupTeacherInfoViews {
    // 头像
    [self.containerView addSubview:self.iconImgV];
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.top.equalTo(16);
        make.width.height.equalTo(40);
    }];
    
    // 姓名和标签
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionEqualSpacing spacing:5 arrangedSubviews:@[self.nameLabel, self.tagView]];
    [self.containerView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImgV.mas_right).offset(9);
        make.centerY.equalTo(self.iconImgV);
    }];
    
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(17);
    }];
    
    // 追随按钮
    [self.containerView addSubview:self.followBtn2];
    [self.followBtn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(self.iconImgV);
        make.width.equalTo(87);
        make.height.equalTo(33);
    }];
    self.followBtn2.hidden = YES;
}

- (void)setupStatisticsViews {
    // 统计数据容器
    UIView *statisticsView = [[UIView alloc] init];
    [self.containerView addSubview:statisticsView];
    [statisticsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(11.5);
        make.right.equalTo(-11.5);
        make.top.equalTo(self.iconImgV.mas_bottom).offset(16.5);
        make.height.equalTo(45);
    }];
    
    // 创建4个统计项
    NSArray *dataLabels = @[self.averageIncreaseLabel, self.upRateLabel, self.maxIncreaseLabel, self.teachingCountLabel];
    NSArray *descLabels = @[self.averageIncreaseDescLabel, self.upRateDescLabel, self.maxIncreaseDescLabel, self.teachingCountDescLabel];
    
    for (int i = 0; i < 4; i++) {
        UIView *itemView = [[UIView alloc] init];
        [statisticsView addSubview:itemView];
        [itemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.bottom.equalTo(0);
            make.width.equalTo(statisticsView).multipliedBy(0.25);
            make.left.equalTo(statisticsView).offset(i * (UI_SCREEN_WIDTH - 53) * 0.25);
        }];
        
        UILabel *dataLabel = dataLabels[i];
        UILabel *descLabel = descLabels[i];
        
        [itemView addSubview:dataLabel];
        [dataLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.top.equalTo(0);
            make.height.equalTo(24);
        }];
        
        [itemView addSubview:descLabel];
        [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.bottom.equalTo(0);
            make.height.equalTo(19);
        }];
    }
}

- (void)setupExpandViews {
    UIStackView *stackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentCenter distribution:UIStackViewDistributionEqualSpacing spacing:10 arrangedSubviews:@[self.followBtn1, self.expandBtn, self.recordTableView]];
    [self.containerView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(7.5);
        make.right.equalTo(-7.5);
        make.top.equalTo(self.iconImgV.mas_bottom).offset(16.5 + 40 + 15);
    }];
    
    [self.recordTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
        make.height.equalTo(0);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 根据排名设置背景色
    if (self.model.rank == 1) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffebc9), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else if (self.model.rank == 2) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xe5f3ff), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else if (self.model.rank == 3) {
        self.containerView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffeadb), FMWhiteColor] withFrame:self.containerView.bounds direction:GradientDirectionTopLeftToBottomRight];
    } else {
        self.containerView.backgroundColor = UIColor.up_contentBgColor;
    }
    
    // 设置标签背景色
    for (ZLTagLabel *tagLabel in self.tagView.subviews) {
        tagLabel.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfeddb5), ColorWithHex(0xfdc48d)] withFrame:tagLabel.bounds direction:GradientDirectionLeftToRight];
    }
}

- (void)setModel:(FMMonthlyTeachingSummaryModel *)model {
    _model = model;
    
    [self updateRankDisplay];
    [self updateTeacherInfo];
    [self updateStatistics];
    [self updateExpandState];
    [self updateRecordTable];
}

- (void)updateRankDisplay {
    if (self.model.rank == 1) {
        // 第一名显示金色皇冠
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"daka_operation_first");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xFFD700), ColorWithHex(0xFFA500)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else if (self.model.rank == 2) {
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"daka_operation_second");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xD2E1EE), ColorWithHex(0x8DAFCA)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else if (self.model.rank == 3) {
        self.rankLabel.hidden = YES;
        self.rankImgV.hidden = NO;
        self.rankImgV.image = ImageWithName(@"daka_operation_third");
        self.iconBgView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffe7d4), ColorWithHex(0xffc196)] withFrame:CGRectMake(0, 0, 40, 40) direction:GradientDirectionTopToBottom];
    } else {
        self.rankImgV.hidden = YES;
        self.rankLabel.hidden = NO;
        self.rankLabel.text = [NSString stringWithFormat:@"%zd", self.model.rank];
        self.iconBgView.backgroundColor = ColorWithHex(0xF2EBEB);
    }
}

- (void)updateTeacherInfo {
    [self.iconImgV sd_setImageWithURL:[NSURL URLWithString:self.model.teacherInfo.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.nameLabel.text = self.model.teacherInfo.userName;
    
    if (self.model.teacherInfo.userGoodAt.length) {
        NSArray *goodAts = [self.model.teacherInfo.userGoodAt componentsSeparatedByString:@"、"];
        self.tagView.titleArray = goodAts;
        self.tagView.hidden = NO;
    } else {
        self.tagView.hidden = YES;
    }
}

- (void)updateStatistics {
    // 平均涨幅
    CGFloat avgIncrease = [self.model.averageIncrease floatValue];
    self.averageIncreaseLabel.text = [NSString stringWithFormat:@"%@%.2f%%", avgIncrease >= 0 ? @"+" : @"", avgIncrease];
    self.averageIncreaseLabel.textColor = avgIncrease >= 0 ? ColorWithHex(0xfc0002) : ColorWithHex(0x00aa00);
    
    // 上涨率
    self.upRateLabel.text = [NSString stringWithFormat:@"%@%%", self.model.upRate];
    self.upRateLabel.textColor = ColorWithHex(0xfc0002);
    
    // 最高涨幅
    CGFloat maxIncrease = [self.model.maxIncrease floatValue];
    self.maxIncreaseLabel.text = [NSString stringWithFormat:@"%@%.2f%%", maxIncrease >= 0 ? @"+" : @"", maxIncrease];
    self.maxIncreaseLabel.textColor = maxIncrease >= 0 ? ColorWithHex(0xfc0002) : ColorWithHex(0x00aa00);
    
    // 教学数
    self.teachingCountLabel.text = [NSString stringWithFormat:@"%zd", self.model.teachingCount];
    self.teachingCountLabel.textColor = FMZeroColor;
}

- (void)updateExpandState {
    if (self.model.isExpanded) {
        
        [self.expandBtn setImage:ImageWithName(@"arrow_up") forState:UIControlStateNormal];
    } else {
        [self.expandBtn setImage:ImageWithName(@"arrow_down") forState:UIControlStateNormal];
    }
}

- (void)updateRecordTable {
    self.recordTableView.model = self.model;
    [self.recordTableView reloadData];
}

#pragma mark - Actions
- (void)followBtnClicked {
    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@&secondLevel=2", self.model.teacherInfo.userId]];
}

- (void)expandBtnClicked {
    BOOL newExpandState = !self.model.isExpanded;
    if (self.expandToggleBlock) {
        self.expandToggleBlock(newExpandState);
    }
}

#pragma mark - Getter
- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [UIView new];
        UI_View_Radius(_containerView, 10);
    }
    return _containerView;
}

- (UIImageView *)rankImgV {
    if (!_rankImgV) {
        _rankImgV = [UIImageView new];
    }
    return _rankImgV;
}

- (ZLTagLabel *)rankLabel {
    if (!_rankLabel) {
        _rankLabel = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(13) textColor:ColorWithHex(0x8b6666) backgroundColor:ColorWithHex(0xf2ebeb) numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _rankLabel.widthPadding = 13;
    }
    return _rankLabel;
}

- (UIImageView *)iconImgV {
    if (!_iconImgV) {
        _iconImgV = [UIImageView new];
    }
    return _iconImgV;
}

- (UILabel *)nameLabel {
    if (!_nameLabel) {
        _nameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    }
    return _nameLabel;
}

- (ZLTagView *)tagView {
    if (!_tagView) {
        _tagView = [[ZLTagView alloc] init];
        _tagView.tagLabelFont = FontWithSize(11);
        _tagView.tagLabelWidthPadding = 8.0f;
        _tagView.tagLabelHeightPadding = 4.0f;
        _tagView.middlePadding = 5;
        _tagView.tagLabelTextColor = ColorWithHex(0xa56112);
        _tagView.tagLabelCornerRadius = 2.0f;
        _tagView.numberofLines = 1;
        _tagView.tagLabelBgColor = ColorWithHex(0xfeddb5);
    }
    return _tagView;
}

- (UIButton *)followBtn1 {
    if (!_followBtn1) {
        _followBtn1 = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(17) normalTextColor:FMWhiteColor backgroundColor:[UIColor lz_gradientColors:@[ColorWithHex(0xfc5219), ColorWithHex(0xfc0002)] withFrame:CGRectMake(0, 0, 150, 40) direction:GradientDirectionLeftToRight] title:@"追随老师" image:nil target:self action:@selector(followBtnClicked)];
        _followBtn1.layer.cornerRadius = 5.0f;
        _followBtn1.layer.shadowColor = ColorWithHex(0xfc0002).CGColor;
        _followBtn1.layer.shadowOffset = CGSizeMake(0, 5);
        _followBtn1.layer.shadowOpacity = 1;
        _followBtn1.layer.shadowRadius = 15.0f;
    }
    return _followBtn1;
}

- (UIButton *)followBtn2 {
    if (!_followBtn2) {
        _followBtn2 = [[UIButton alloc] initWithFrame:CGRectZero font:BoldFontWithSize(14) normalTextColor:FMWhiteColor backgroundColor:[UIColor lz_gradientColors:@[ColorWithHex(0xfc5219), ColorWithHex(0xfc0002)] withFrame:CGRectMake(0, 0, 87, 33) direction:GradientDirectionLeftToRight] title:@"追随老师" image:nil target:self action:@selector(followBtnClicked)];
        UI_View_Radius(_followBtn2, 5.0f);
    }
    return _followBtn2;
}

- (UILabel *)averageIncreaseLabel {
    if (!_averageIncreaseLabel) {
        _averageIncreaseLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _averageIncreaseLabel;
}

- (UILabel *)averageIncreaseDescLabel {
    if (!_averageIncreaseDescLabel) {
        _averageIncreaseDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _averageIncreaseDescLabel.text = @"平均涨幅";
    }
    return _averageIncreaseDescLabel;
}

- (UILabel *)upRateLabel {
    if (!_upRateLabel) {
        _upRateLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _upRateLabel;
}

- (UILabel *)upRateDescLabel {
    if (!_upRateDescLabel) {
        _upRateDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _upRateDescLabel.text = @"上涨率";
    }
    return _upRateDescLabel;
}

- (UILabel *)maxIncreaseLabel {
    if (!_maxIncreaseLabel) {
        _maxIncreaseLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _maxIncreaseLabel;
}

- (UILabel *)maxIncreaseDescLabel {
    if (!_maxIncreaseDescLabel) {
        _maxIncreaseDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _maxIncreaseDescLabel.text = @"最高涨幅";
    }
    return _maxIncreaseDescLabel;
}

- (UILabel *)teachingCountLabel {
    if (!_teachingCountLabel) {
        _teachingCountLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(18) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _teachingCountLabel;
}

- (UILabel *)teachingCountDescLabel {
    if (!_teachingCountDescLabel) {
        _teachingCountDescLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x888888) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        _teachingCountDescLabel.text = @"教学数";
    }
    return _teachingCountDescLabel;
}

- (UIButton *)expandBtn {
    if (!_expandBtn) {
        _expandBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12) normalTextColor:ColorWithHex(0xa56112) backgroundColor:FMClearColor title:nil image:ImageWithName(@"daka_monthlyTeaching_arrow") target:self action:@selector(expandBtnClicked)];
        [_expandBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:5];
    }
    return _expandBtn;
}

- (FMTeachingRecordTableView *)recordTableView {
    if (!_recordTableView) {
        _recordTableView = [[FMTeachingRecordTableView alloc] init];
    }
    return _recordTableView;
}

@end
