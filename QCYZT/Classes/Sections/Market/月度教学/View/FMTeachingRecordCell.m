//
//  FMTeachingRecordCell.m
//  QCYZT
//
//  Created by Augment on 2025-09-01
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMTeachingRecordCell.h"
#import "FMUPDataTool.h"

@interface FMTeachingRecordCell()

@property (nonatomic, strong) UILabel *stockAliasLabel;
@property (nonatomic, strong) UILabel *stockNameLabel;
@property (nonatomic, strong) UILabel *timeRangeLabel;
@property (nonatomic, strong) UILabel *increaseLabel;

@end

@implementation FMTeachingRecordCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleDefault;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.contentView.backgroundColor = FMWhiteColor;
    
    CGFloat itemWidth = (UI_SCREEN_WIDTH - 60) / 4;
    
    // 教学代称
    [self.contentView addSubview:self.stockAliasLabel];
    [self.stockAliasLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.width.equalTo(@(itemWidth));
        make.left.equalTo(@0);
    }];
    
    // 股票名称
    [self.contentView addSubview:self.stockNameLabel];
    [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.width.equalTo(@(itemWidth));
        make.left.equalTo(@(itemWidth));
    }];
    
    // 时间范围
    [self.contentView addSubview:self.timeRangeLabel];
    [self.timeRangeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.width.equalTo(@(itemWidth));
        make.left.equalTo(@(itemWidth * 2));
    }];
    
    // 区间涨幅
    [self.contentView addSubview:self.increaseLabel];
    [self.increaseLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(@0);
        make.width.equalTo(@(itemWidth));
        make.left.equalTo(@(itemWidth * 3));
    }];
    
    // 底部分割线
    UIView *separatorLine = [[UIView alloc] init];
    separatorLine.backgroundColor = ColorWithHex(0xf0f0f0);
    [self.contentView addSubview:separatorLine];
    [separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.height.equalTo(@0.5);
    }];
}

- (void)setModel:(FMTeachingRecordModel *)model {
    _model = model;
    
    // 教学代称
    self.stockAliasLabel.text = model.stockAlias ?: @"";
    
    // 股票名称（可点击跳转）
    self.stockNameLabel.text = model.stockName ?: @"";
    self.stockNameLabel.userInteractionEnabled = YES;
    [self.stockNameLabel bk_whenTapped:^{
        [self jumpToStockDetail];
    }];
    
    // 时间范围：买入时间-卖出时间
    NSString *timeRange = [self formatTimeRange:model.createTime sellTime:model.sellTime];
    self.timeRangeLabel.text = timeRange;
    
    // 区间涨幅
    CGFloat increaseValue = [model.increase floatValue];
    self.increaseLabel.text = [NSString stringWithFormat:@"%@%.2f%%", increaseValue >= 0 ? @"+" : @"", increaseValue];
    self.increaseLabel.textColor = increaseValue >= 0 ? ColorWithHex(0xfc0002) : ColorWithHex(0x00aa00);
}

- (NSString *)formatTimeRange:(NSString *)createTime sellTime:(NSString *)sellTime {
    if (!createTime.length || !sellTime.length) {
        return @"";
    }
    
    // 处理时间格式：如果是当年则不显示年份，否则显示后两位年份
    NSString *formattedCreateTime = [self formatSingleTime:createTime];
    NSString *formattedSellTime = [self formatSingleTime:sellTime];
    
    return [NSString stringWithFormat:@"%@-%@", formattedCreateTime, formattedSellTime];
}

- (NSString *)formatSingleTime:(NSString *)timeStr {
    if (!timeStr.length) return @"";
    
    // 假设输入格式为 "24.06.05" 或 "2024.06.05"
    NSArray *components = [timeStr componentsSeparatedByString:@"."];
    if (components.count >= 3) {
        NSString *year = components[0];
        NSString *month = components[1];
        NSString *day = components[2];
        
        // 获取当前年份
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        [formatter setDateFormat:@"yyyy"];
        NSString *currentYear = [formatter stringFromDate:[NSDate date]];
        
        // 如果年份是两位数，补充为四位数
        if (year.length == 2) {
            year = [NSString stringWithFormat:@"20%@", year];
        }
        
        // 如果是当年，不显示年份
        if ([year isEqualToString:currentYear]) {
            return [NSString stringWithFormat:@"%@.%@", month, day];
        } else {
            // 显示后两位年份
            NSString *shortYear = [year substringFromIndex:2];
            return [NSString stringWithFormat:@"%@.%@.%@", shortYear, month, day];
        }
    }
    
    return timeStr;
}

- (void)jumpToStockDetail {
    if (!self.model.stockCode.length) return;
    
    UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:self.model.stockCode];
    if (matchInfo) {
        [UPRouterUtil goMarketStock:matchInfo.setCode code:matchInfo.code];
    }
}

#pragma mark - Getter
- (UILabel *)stockAliasLabel {
    if (!_stockAliasLabel) {
        _stockAliasLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _stockAliasLabel;
}

- (UILabel *)stockNameLabel {
    if (!_stockNameLabel) {
        _stockNameLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x0074fa) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _stockNameLabel;
}

- (UILabel *)timeRangeLabel {
    if (!_timeRangeLabel) {
        _timeRangeLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMZeroColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _timeRangeLabel;
}

- (UILabel *)increaseLabel {
    if (!_increaseLabel) {
        _increaseLabel = [[UILabel alloc] initWithFrame:CGRectZero font:BoldFontWithSize(12) textColor:ColorWithHex(0xfc0002) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    }
    return _increaseLabel;
}

@end
