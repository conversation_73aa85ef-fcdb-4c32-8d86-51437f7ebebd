//
//  FMTeachingRecordTableView.m
//  QCYZT
//
//  Created by Augment on 2025-09-01
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMTeachingRecordTableView.h"
#import "FMTeachingRecordCell.h"

@interface FMTeachingRecordTableView ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIView *headerView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray<FMTeachingRecordModel *> *displayRecords;

@end

@implementation FMTeachingRecordTableView

- (instancetype)init {
    if (self = [super init]) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = FMClearColor;
    
    // 表头
    [self addSubview:self.headerView];
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@0);
        make.height.equalTo(@40);
    }];
    
    // 表格
    [self addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.top.equalTo(self.headerView.mas_bottom);
    }];
}

- (void)setModel:(FMMonthlyTeachingSummaryModel *)model {
    _model = model;
    
    if (model.isExpanded) {
        // 展开状态：显示最多8条记录
        NSInteger maxCount = MIN(model.teachingRecords.count, 8);
        self.displayRecords = [model.teachingRecords subarrayWithRange:NSMakeRange(0, maxCount)];
    } else {
        // 折叠状态：显示3条记录
        NSInteger maxCount = MIN(model.teachingRecords.count, 3);
        self.displayRecords = [model.teachingRecords subarrayWithRange:NSMakeRange(0, maxCount)];
    }
}

- (void)reloadData {
    [self.tableView reloadData];
}

#pragma mark - UITableViewDataSource & UITableViewDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.displayRecords.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMTeachingRecordCell *cell = [tableView reuseCellClass:[FMTeachingRecordCell class]];
    cell.model = self.displayRecords[indexPath.row];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 40;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    FMTeachingRecordModel *record = self.displayRecords[indexPath.row];
    
    // 如果有笔记ID，跳转到笔记详情
    if (record.noteId.length) {
        [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://note?id=%@", record.noteId]];
    }
}

#pragma mark - Getter
- (UIView *)headerView {
    if (!_headerView) {
        _headerView = [[UIView alloc] init];
        _headerView.backgroundColor = ColorWithHex(0xf8f8f8);
        
        // 表头标题
        NSArray *titles = @[@"教学代称", @"股票名称", @"时间范围", @"区间涨幅"];
        CGFloat itemWidth = (UI_SCREEN_WIDTH - 60) / 4; // 减去左右边距和间距
        
        for (int i = 0; i < titles.count; i++) {
            UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
            titleLabel.text = titles[i];
            [_headerView addSubview:titleLabel];
            
            [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerY.equalTo(@0);
                make.width.equalTo(@(itemWidth));
                make.left.equalTo(@(i * itemWidth));
            }];
        }
        
        // 底部分割线
        UIView *separatorLine = [[UIView alloc] init];
        separatorLine.backgroundColor = ColorWithHex(0xe5e5e5);
        [_headerView addSubview:separatorLine];
        [separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(@0);
            make.height.equalTo(@0.5);
        }];
    }
    
    return _headerView;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:nil];
        [_tableView registerCellClass:[FMTeachingRecordCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = FMClearColor;
        _tableView.showsVerticalScrollIndicator = YES;
        _tableView.scrollEnabled = YES;
        _tableView.bounces = YES;
    }
    
    return _tableView;
}

@end
