//
//  FMMonthlyTeachingSummaryViewController.m
//  QCYZT
//
//  Created by Windsurf on 2025-09-01
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMMonthlyTeachingSummaryViewController.h"
#import "FMMonthlyTeachingRankCell.h"
#import "FMMonthlyTeachingSummaryModel.h"
#import "HttpRequestTool+Daka.h"

typedef NS_ENUM(NSInteger, FMTeachingSortType) {
    FMTeachingSortTypeAverageIncrease = 0,  // 平均涨幅
    FMTeachingSortTypeUpRate,               // 上涨率
    FMTeachingSortTypeMaxIncrease,          // 最高涨幅
    FMTeachingSortTypeTeachingCount         // 教学数
};

@interface FMMonthlyTeachingSummaryViewController ()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) UIView *filterView;
@property (nonatomic, strong) UIButton *timeFilterBtn;
@property (nonatomic, strong) UIButton *sortFilterBtn;

@property (nonatomic, strong) NSArray<FMMonthlyTeachingSummaryModel *> *dataArr;
@property (nonatomic, assign) FMTeachingSortType currentSortType;
@property (nonatomic, copy) NSString *currentTimeRange;
@property (nonatomic, assign) NSInteger expandedIndex; // 当前展开的cell索引

@end

@implementation FMMonthlyTeachingSummaryViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    [self setupDefaultValues];
    [self requestData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self configNavRedColor];
}

- (void)setupUI {
    self.view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;

    // 筛选栏
    [self.view addSubview:self.filterView];
    [self.filterView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@0);
        make.height.equalTo(@60);
    }];
    
    // 表格
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(@0);
        make.top.equalTo(self.filterView.mas_bottom);
    }];
}

- (void)setupDefaultValues {
    self.currentSortType = FMTeachingSortTypeAverageIncrease;
    self.currentTimeRange = @"近一周";
    self.expandedIndex = 0; // 默认展开第一个
    
    [self.timeFilterBtn setTitle:[NSString stringWithFormat:@"时间范围：%@", self.currentTimeRange] forState:UIControlStateNormal];
    [self.sortFilterBtn setTitle:@"排序：平均涨幅" forState:UIControlStateNormal];
}

- (void)requestData {
    NSString *sortTypeStr = [self getSortTypeString:self.currentSortType];
    
    [HttpRequestTool getMonthlyTeachingSummaryWithTimeRange:self.currentTimeRange
                                                   sortType:sortTypeStr
                                                  advisorId:self.advisorId
                                                      start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
        // 失败时使用模拟数据
        [self loadMockData];
    } success:^(NSDictionary *dic) {
        [SVProgressHUD dismiss];
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray *dataArray = dic[@"data"];
            if ([dataArray isKindOfClass:[NSArray class]]) {
                self.dataArr = [NSArray modelArrayWithClass:[FMMonthlyTeachingSummaryModel class] json:dataArray];
                // 设置默认展开第一个
                if (self.dataArr.count > 0) {
                    FMMonthlyTeachingSummaryModel *firstModel = self.dataArr[0];
                    firstModel.isExpanded = YES;
                    self.expandedIndex = 0;
                }
                [self.tableView reloadData];
            } else {
                [self loadMockData];
            }
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"] ?: @"请求失败"];
            [self loadMockData];
        }
    }];
}

- (void)loadMockData {
    NSMutableArray *mockData = [NSMutableArray array];
    
    // 投顾姓名和擅长领域
    NSArray *names = @[@"刘安辉", @"王博", @"廖敏", @"任连均", @"张明华", @"李志强", @"陈晓雯", @"赵国庆", @"孙海涛", @"周建军"];
    NSArray *goodAts = @[
        @"短线操手、涨停王者",
        @"价值投资、长线布局", 
        @"技术分析、趋势跟踪",
        @"热点挖掘、题材炒作",
        @"量化交易、风险控制",
        @"基本面分析、成长股",
        @"波段操作、资金管理",
        @"新股申购、打板专家",
        @"期权策略、对冲套利",
        @"宏观分析、板块轮动"
    ];
    
    // 股票数据池
    NSArray *stockData = @[
        @{@"name": @"中码传动", @"code": @"301123", @"alias": @"辉腾"},
        @{@"name": @"高伟达", @"code": @"300465", @"alias": @"博金"},
        @{@"name": @"长盈精密", @"code": @"300115", @"alias": @"精工"},
        @{@"name": @"东方财富", @"code": @"300059", @"alias": @"财富"},
        @{@"name": @"比亚迪", @"code": @"002594", @"alias": @"新能源"},
        @{@"name": @"宁德时代", @"code": @"300750", @"alias": @"电池王"},
        @{@"name": @"贵州茅台", @"code": @"600519", @"alias": @"白酒龙头"},
        @{@"name": @"中国平安", @"code": @"601318", @"alias": @"保险巨头"},
        @{@"name": @"腾讯控股", @"code": @"00700", @"alias": @"互联网"},
        @{@"name": @"阿里巴巴", @"code": @"09988", @"alias": @"电商"},
        @{@"name": @"美的集团", @"code": @"000333", @"alias": @"家电"},
        @{@"name": @"五粮液", @"code": @"000858", @"alias": @"白酒"},
        @{@"name": @"招商银行", @"code": @"600036", @"alias": @"银行"},
        @{@"name": @"海康威视", @"code": @"002415", @"alias": @"安防"},
        @{@"name": @"立讯精密", @"code": @"002475", @"alias": @"精密制造"}
    ];
    
    // 创建10个投顾的模拟数据
    for (int i = 0; i < 10; i++) {
        FMMonthlyTeachingSummaryModel *model = [[FMMonthlyTeachingSummaryModel alloc] init];
        model.rank = i + 1;
        
        // 投顾信息
        FMTeacherInfoModel *teacherInfo = [[FMTeacherInfoModel alloc] init];
        teacherInfo.userName = names[i];
        teacherInfo.userGoodAt = goodAts[i];
        teacherInfo.userId = [NSString stringWithFormat:@"advisor_%d", i+1];
        teacherInfo.userIco = @""; // 头像URL
        model.teacherInfo = teacherInfo;
        
        // 教学记录数量：第一个老师12条，第二个8条，其他随机3-6条
        NSInteger recordCount;
        if (i == 0) {
            recordCount = 12; // 第一个老师最多记录，用于测试滑动
        } else if (i == 1) {
            recordCount = 8;  // 第二个老师8条记录
        } else {
            recordCount = 3 + (i % 4); // 其他老师3-6条记录
        }
        
        NSMutableArray *records = [NSMutableArray array];
        for (int j = 0; j < recordCount; j++) {
            FMTeachingRecordModel *record = [[FMTeachingRecordModel alloc] init];
            
            // 随机选择股票
            NSDictionary *stock = stockData[j % stockData.count];
            record.stockName = stock[@"name"];
            record.stockCode = stock[@"code"];
            record.stockAlias = [NSString stringWithFormat:@"%@%d号", stock[@"alias"], j+1];
            
            // 随机生成时间
            NSArray *dates = @[@"24.08.01", @"24.08.05", @"24.08.12", @"24.08.15", @"24.08.20", @"24.08.25"];
            NSArray *sellDates = @[@"24.08.08", @"24.08.12", @"24.08.19", @"24.08.22", @"24.08.27", @"24.09.01"];
            record.createTime = dates[j % dates.count];
            record.sellTime = sellDates[j % sellDates.count];
            
            // 生成涨跌幅：70%概率为正收益，30%为负收益
            BOOL isPositive = (arc4random() % 100) < 70;
            if (isPositive) {
                // 正收益：0.5% - 35%
                CGFloat increase = 0.5 + (arc4random() % 3450) / 100.0;
                record.increase = [NSString stringWithFormat:@"%.2f", increase];
            } else {
                // 负收益：-0.5% - -15%
                CGFloat decrease = -(0.5 + (arc4random() % 1450) / 100.0);
                record.increase = [NSString stringWithFormat:@"%.2f", decrease];
            }
            
            // 随机价格
            CGFloat buyPrice = 8.0 + (arc4random() % 5000) / 100.0; // 8-58元
            CGFloat increaseValue = [record.increase floatValue];
            CGFloat sellPrice = buyPrice * (1 + increaseValue / 100.0);
            record.buyPrice = [NSString stringWithFormat:@"%.2f", buyPrice];
            record.sellPrice = [NSString stringWithFormat:@"%.2f", sellPrice];
            
            // 部分记录有笔记ID
            if (j % 3 == 0) {
                record.noteId = [NSString stringWithFormat:@"note_%d_%d", i+1, j+1];
            }
            
            [records addObject:record];
        }
        model.teachingRecords = records;
        
        // 计算统计数据
        [model calculateStatistics];
        
        // 设置展开状态：默认展开第一个
        model.isExpanded = (i == 0);
        
        [mockData addObject:model];
    }
    
    // 按平均涨幅排序（模拟排序效果）
    [mockData sortUsingComparator:^NSComparisonResult(FMMonthlyTeachingSummaryModel *obj1, FMMonthlyTeachingSummaryModel *obj2) {
        CGFloat avg1 = [obj1.averageIncrease floatValue];
        CGFloat avg2 = [obj2.averageIncrease floatValue];
        if (avg1 > avg2) return NSOrderedAscending;
        if (avg1 < avg2) return NSOrderedDescending;
        return NSOrderedSame;
    }];
    
    // 重新设置排名
    for (int i = 0; i < mockData.count; i++) {
        FMMonthlyTeachingSummaryModel *model = mockData[i];
        model.rank = i + 1;
        model.isExpanded = (i == 0); // 重新设置展开状态
    }
    
    self.dataArr = mockData;
    self.expandedIndex = 0;
    [self.tableView reloadData];
}

#pragma mark - Actions
- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)timeFilterClicked {
    // TODO: 显示时间筛选弹窗
    NSLog(@"时间筛选");
}

- (void)sortFilterClicked {
    // TODO: 显示排序筛选弹窗
    NSLog(@"排序筛选");
}

#pragma mark - UITableViewDataSource & UITableViewDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMMonthlyTeachingRankCell *cell = [tableView reuseCellClass:[FMMonthlyTeachingRankCell class]];
    cell.model = self.dataArr[indexPath.row];
    cell.isLastCell = (indexPath.row == self.dataArr.count - 1);
    
    // 设置cell回调
    __weak typeof(self) weakSelf = self;
    cell.expandToggleBlock = ^(BOOL isExpanded) {
        [weakSelf handleCellExpandToggle:indexPath.row isExpanded:isExpanded];
    };
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMMonthlyTeachingSummaryModel *model = self.dataArr[indexPath.row];
    
    // 基础高度：排名 + 投顾信息 + 统计数据 + 追随按钮
    CGFloat baseHeight = 200;
    
    if (model.isExpanded) {
        // 展开状态：显示教学记录列表
        NSInteger recordCount = MIN(model.teachingRecords.count, 8); // 最多显示8条
        CGFloat recordHeight = recordCount * 40 + 50; // 每条记录40高度 + 表头50
        baseHeight += recordHeight;
    } else {
        // 折叠状态：显示3条记录
        baseHeight += 3 * 40 + 50;
    }
    
    return baseHeight;
}

- (void)handleCellExpandToggle:(NSInteger)index isExpanded:(BOOL)isExpanded {
    // 收起之前展开的cell
    if (self.expandedIndex != index && self.expandedIndex < self.dataArr.count) {
        FMMonthlyTeachingSummaryModel *previousModel = self.dataArr[self.expandedIndex];
        previousModel.isExpanded = NO;
    }
    
    // 更新当前cell状态
    FMMonthlyTeachingSummaryModel *currentModel = self.dataArr[index];
    currentModel.isExpanded = isExpanded;
    self.expandedIndex = isExpanded ? index : -1;
    
    // 刷新表格
    [self.tableView reloadData];
}

- (NSString *)getSortTypeString:(FMTeachingSortType)sortType {
    switch (sortType) {
        case FMTeachingSortTypeAverageIncrease:
            return @"averageIncrease";
        case FMTeachingSortTypeUpRate:
            return @"upRate";
        case FMTeachingSortTypeMaxIncrease:
            return @"maxIncrease";
        case FMTeachingSortTypeTeachingCount:
            return @"teachingCount";
        default:
            return @"averageIncrease";
    }
}

#pragma mark - Getter
- (UIView *)filterView {
    if (!_filterView) {
        _filterView = [UIView new];
        _filterView.backgroundColor = FMWhiteColor;
        
        [_filterView addSubview:self.timeFilterBtn];
        [self.timeFilterBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.centerY.equalTo(@0);
            make.height.equalTo(@30);
        }];
        
        [_filterView addSubview:self.sortFilterBtn];
        [self.sortFilterBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(@-15);
            make.centerY.equalTo(@0);
            make.height.equalTo(@30);
        }];
        
        // 底部分割线
        UIView *separatorLine = [[UIView alloc] init];
        separatorLine.backgroundColor = ColorWithHex(0xe5e5e5);
        [_filterView addSubview:separatorLine];
        [separatorLine mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.bottom.equalTo(@0);
            make.height.equalTo(@0.5);
        }];
    }
    
    return _filterView;
}

- (UIButton *)timeFilterBtn {
    if (!_timeFilterBtn) {
        _timeFilterBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:ColorWithHex(0x333333) backgroundColor:FMClearColor title:@"时间范围：近一周" image:ImageWithName(@"arrow_down_gray") target:self action:@selector(timeFilterClicked)];
        _timeFilterBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
        [_timeFilterBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 20)];
        [_timeFilterBtn setImageEdgeInsets:UIEdgeInsetsMake(0, 0, 0, -20)];
        
        // 添加虚线边框
        _timeFilterBtn.layer.borderWidth = 1.0;
        _timeFilterBtn.layer.borderColor = ColorWithHex(0xcccccc).CGColor;
        _timeFilterBtn.layer.cornerRadius = 4.0;
        // TODO: 实现虚线边框
    }
    
    return _timeFilterBtn;
}

- (UIButton *)sortFilterBtn {
    if (!_sortFilterBtn) {
        _sortFilterBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:ColorWithHex(0x333333) backgroundColor:FMClearColor title:@"排序：平均涨幅" image:ImageWithName(@"arrow_down_gray") target:self action:@selector(sortFilterClicked)];
        _sortFilterBtn.contentHorizontalAlignment = UIControlContentHorizontalAlignmentLeft;
        [_sortFilterBtn setTitleEdgeInsets:UIEdgeInsetsMake(0, 0, 0, 20)];
        [_sortFilterBtn setImageEdgeInsets:UIEdgeInsetsMake(0, 0, 0, -20)];
        
        // 添加虚线边框
        _sortFilterBtn.layer.borderWidth = 1.0;
        _sortFilterBtn.layer.borderColor = ColorWithHex(0xcccccc).CGColor;
        _sortFilterBtn.layer.cornerRadius = 4.0;
        // TODO: 实现虚线边框
    }
    
    return _sortFilterBtn;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self];
        [_tableView registerCellClass:[FMMonthlyTeachingRankCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = ColorWithHex(0xf5f5f5);
        _tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 20)];
        _tableView.showsVerticalScrollIndicator = NO;
    }
    
    return _tableView;
}

@end
