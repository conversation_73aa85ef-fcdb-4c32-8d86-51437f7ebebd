//
//  FMMonthlyTeachingSummaryModel.m
//  QCYZT
//
//  Created by Windsurf on 2025-09-01
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import "FMMonthlyTeachingSummaryModel.h"

@implementation FMTeachingRecordModel

@end

@implementation FMTeacherInfoModel

@end

@implementation FMMonthlyTeachingSummaryModel

- (instancetype)init {
    if (self = [super init]) {
        _isExpanded = NO; // 默认折叠状态
    }
    return self;
}

- (void)calculateStatistics {
    if (!self.teachingRecords.count) {
        self.averageIncrease = @"0.00";
        self.upRate = @"0";
        self.maxIncrease = @"0.00";
        self.teachingCount = 0;
        return;
    }
    
    self.teachingCount = self.teachingRecords.count;
    
    // 计算平均涨幅
    CGFloat totalIncrease = 0.0;
    CGFloat maxIncreaseValue = 0.0;
    NSInteger upCount = 0;
    
    for (FMTeachingRecordModel *record in self.teachingRecords) {
        CGFloat increaseValue = [record.increase floatValue];
        totalIncrease += increaseValue;
        
        if (increaseValue > 0) {
            upCount++;
        }
        
        if (increaseValue > maxIncreaseValue) {
            maxIncreaseValue = increaseValue;
        }
    }
    
    // 平均涨幅
    CGFloat avgIncrease = totalIncrease / self.teachingCount;
    self.averageIncrease = [NSString stringWithFormat:@"%.2f", avgIncrease];
    
    // 上涨率
    CGFloat upRateValue = (CGFloat)upCount / self.teachingCount * 100;
    self.upRate = [NSString stringWithFormat:@"%.0f", upRateValue];
    
    // 最高涨幅
    self.maxIncrease = [NSString stringWithFormat:@"%.2f", maxIncreaseValue];
}

@end
