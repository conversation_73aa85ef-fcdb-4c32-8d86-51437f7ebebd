//
//  FMMonthlyTeachingSummaryModel.h
//  QCYZT
//
//  Created by Windsurf on 2025-09-01
//  Copyright © 2025 LZKJ. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 教学记录模型
@interface FMTeachingRecordModel : NSObject

@property (nonatomic, copy) NSString *stockAlias;      // 教学代称
@property (nonatomic, copy) NSString *stockName;       // 股票名称
@property (nonatomic, copy) NSString *stockCode;       // 股票代码
@property (nonatomic, copy) NSString *createTime;      // 买入时间
@property (nonatomic, copy) NSString *sellTime;        // 卖出时间
@property (nonatomic, copy) NSString *increase;        // 区间涨幅
@property (nonatomic, copy) NSString *noteId;          // 笔记ID（可选）
@property (nonatomic, copy) NSString *buyPrice;        // 买入价格
@property (nonatomic, copy) NSString *sellPrice;       // 卖出价格

@end

// 投顾信息模型
@interface FMTeacherInfoModel : NSObject

@property (nonatomic, copy) NSString *userId;          // 投顾ID
@property (nonatomic, copy) NSString *userName;        // 投顾名称
@property (nonatomic, copy) NSString *userIco;         // 投顾头像
@property (nonatomic, copy) NSString *userGoodAt;      // 擅长领域

@end

// 月度教学排行榜模型
@interface FMMonthlyTeachingSummaryModel : NSObject

@property (nonatomic, strong) FMTeacherInfoModel *teacherInfo;         // 投顾信息
@property (nonatomic, strong) NSArray<FMTeachingRecordModel *> *teachingRecords;  // 教学记录列表
@property (nonatomic, assign) NSInteger rank;                          // 排名
@property (nonatomic, copy) NSString *averageIncrease;                 // 平均涨幅
@property (nonatomic, copy) NSString *upRate;                          // 上涨率
@property (nonatomic, copy) NSString *maxIncrease;                     // 最高涨幅
@property (nonatomic, assign) NSInteger teachingCount;                 // 教学数
@property (nonatomic, assign) BOOL isExpanded;                         // 是否展开状态

// 计算统计数据的方法
- (void)calculateStatistics;

@end

NS_ASSUME_NONNULL_END
