# 月度教学汇总功能

## 功能概述
月度教学汇总页面用于展示投顾老师在指定时间范围内的教学数据统计和详细记录，支持按不同维度排序和筛选。

## 文件结构
```
月度教学/
├── Controller/
│   └── FMMonthlyTeachingSummaryViewController (.h/.m)  // 主控制器
├── View/
│   ├── FMMonthlyTeachingRankCell (.h/.m)              // 排行榜Cell
│   ├── FMTeachingRecordTableView (.h/.m)              // 教学记录表格
│   └── FMTeachingRecordCell (.h/.m)                   // 教学记录Cell
├── Model/
│   └── FMMonthlyTeachingSummaryModel (.h/.m)          // 数据模型
└── README.md                                          // 说明文档
```

## 使用方法

### 1. 基本使用
```objective-c
// 创建月度教学汇总页面
FMMonthlyTeachingSummaryViewController *vc = [[FMMonthlyTeachingSummaryViewController alloc] init];
[self.navigationController pushViewController:vc animated:YES];
```

### 2. 单投顾数据展示
```objective-c
// 仅展示特定投顾的教学数据
FMMonthlyTeachingSummaryViewController *vc = [[FMMonthlyTeachingSummaryViewController alloc] init];
vc.advisorId = @"投顾ID";
[self.navigationController pushViewController:vc animated:YES];
```

### 3. 路由跳转
```objective-c
// 通过协议跳转
[ProtocolJump jumpWithUrl:@"qcyzt://monthlyTeaching"];

// 带参数跳转
[ProtocolJump jumpWithUrl:@"qcyzt://monthlyTeaching?advisorId=123"];
```

## 核心功能

### 1. 展开收起机制
- 默认展开第一个老师的教学记录
- 折叠状态显示3条教学记录
- 展开状态显示最多8条教学记录
- 超出8条的记录可在Cell内部滑动查看

### 2. 数据统计
- **平均涨幅**: 该老师所有教学记录的平均涨跌幅
- **上涨率**: 上涨记录占总记录的百分比
- **最高涨幅**: 该老师教学记录中的最高涨幅
- **教学数**: 该老师的教学记录总数

### 3. 交互功能
- 点击老师头像/姓名/擅长 → 跳转投顾主页教学tab
- 点击股票名称 → 跳转个股详情页面
- 点击教学记录（除股票名称外） → 跳转笔记详情（如有配置）
- 点击展开收起按钮 → 切换教学记录显示状态

### 4. 筛选排序
- **时间筛选**: 支持选择不同时间范围的数据
- **排序方式**: 支持按平均涨幅、上涨率、最高涨幅、教学数排序

## API接口

### 网络请求
```objective-c
// 获取月度教学汇总数据
[HttpRequestTool getMonthlyTeachingSummaryWithTimeRange:@"近一周"
                                               sortType:@"averageIncrease"
                                              advisorId:nil
                                                  start:^{
    // 开始请求
} failure:^{
    // 请求失败
} success:^(NSDictionary *dic) {
    // 请求成功
}];
```

### 参数说明
- `timeRange`: 时间范围 ("近一周", "近一月", "近三月" 等)
- `sortType`: 排序类型 ("averageIncrease", "upRate", "maxIncrease", "teachingCount")
- `advisorId`: 投顾ID (可选，不传则获取所有投顾数据)

## 数据模型

### FMMonthlyTeachingSummaryModel
```objective-c
@property (nonatomic, strong) FMTeacherInfoModel *teacherInfo;         // 投顾信息
@property (nonatomic, strong) NSArray<FMTeachingRecordModel *> *teachingRecords;  // 教学记录
@property (nonatomic, assign) NSInteger rank;                          // 排名
@property (nonatomic, copy) NSString *averageIncrease;                 // 平均涨幅
@property (nonatomic, copy) NSString *upRate;                          // 上涨率
@property (nonatomic, copy) NSString *maxIncrease;                     // 最高涨幅
@property (nonatomic, assign) NSInteger teachingCount;                 // 教学数
@property (nonatomic, assign) BOOL isExpanded;                         // 是否展开
```

### FMTeachingRecordModel
```objective-c
@property (nonatomic, copy) NSString *stockAlias;      // 教学代称
@property (nonatomic, copy) NSString *stockName;       // 股票名称
@property (nonatomic, copy) NSString *stockCode;       // 股票代码
@property (nonatomic, copy) NSString *createTime;      // 买入时间
@property (nonatomic, copy) NSString *sellTime;        // 卖出时间
@property (nonatomic, copy) NSString *increase;        // 区间涨幅
@property (nonatomic, copy) NSString *noteId;          // 笔记ID
```

## 注意事项

1. **性能优化**: 大量教学记录时，Cell高度会动态计算，建议做好缓存
2. **网络处理**: 包含网络请求失败时的模拟数据fallback机制
3. **UI适配**: 已适配不同排名的背景色和图标显示
4. **时间格式**: 自动处理时间显示格式，当年不显示年份
5. **颜色显示**: 涨跌幅根据正负值显示不同颜色

## 扩展功能

### 待实现功能
- [ ] 时间筛选弹窗
- [ ] 排序筛选弹窗
- [ ] 数据导出功能
- [ ] 更多统计维度

### 自定义扩展
可以通过继承或扩展现有类来实现更多功能：
- 自定义排序规则
- 添加更多统计指标
- 自定义UI样式
- 添加数据分析图表
