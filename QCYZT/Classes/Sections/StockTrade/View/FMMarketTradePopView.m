//
//  FMMarketTradePopView.m
//  QCYZT
//
//  Created by zeng on 2021/11/25.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMMarketTradePopView.h"
#import "FMUpDownButton.h"
#import "FMChooseBrokerViewController.h"
#import "HttpRequestTool+Stock.h"
#import "FMWKWebViewController.h"
#import "FMUPDataTool.h"

static const CGFloat kContentViewHeight = 150.0f;

@interface FMMarketTradePopView()

@property (nonatomic, strong) UIView *bgView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIButton *closeBtn;
@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) NSArray *btnDataArr;

@end

@implementation FMMarketTradePopView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setUp];
    }
    
    return self;
}

- (void)setUp {
    UIView *bgView = [[UIView alloc] init];
    [self addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsZero);
    }];
    bgView.backgroundColor = ColorWithHexAlpha(0x000000, 0.5);
    self.bgView = bgView;
    
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, kContentViewHeight + UI_SAFEAREA_BOTTOM_HEIGHT)];
    contentView.backgroundColor = FMWhiteColor;
    [bgView addSubview:contentView];
    [contentView layerAndBezierPathWithRect:contentView.bounds cornerRadii:CGSizeMake(15, 15) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight];
    self.contentView = contentView;
    
    UIView *topView = [[UIView alloc] init];
    [contentView addSubview:topView];
    [topView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(@0);
        make.height.equalTo(@60);
    }];
    
    UIButton *chooseBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12) normalTextColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor title:@"切换券商" image:nil target:self action:@selector(chooseBroker)];
    [topView addSubview:chooseBtn];
    [chooseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.centerY.equalTo(@0);
    }];
        
    UIButton *closeBtn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMWhiteColor title:nil image:ImageWithName(@"stock_close") target:self action:@selector(close)];
    [topView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.top.equalTo(@0);
        make.width.height.equalTo(@46);
    }];
    self.closeBtn = closeBtn;
    
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(17) textColor:FMZeroColor backgroundColor:FMWhiteColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
    [topView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(@0);
    }];
    self.titleLabel = titleLabel;
    
    UIView *sepLine = [contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(topView.mas_bottom);
        make.height.equalTo(@0.7);
    }];
    
    // 根据开户链接配置过滤按钮数据
    NSMutableArray *filteredBtnData = [NSMutableArray array];
    for (NSInteger i = 0; i < self.btnDataArr.count; i++) {
        NSDictionary *dic = self.btnDataArr[i];
        // 如果是开户按钮且没有配置开户链接，则跳过
        if ([dic[@"title"] isEqualToString:@"开户"] && (!self.choosedBrokerModel.openUrl || self.choosedBrokerModel.openUrl.length == 0)) {
            continue;
        }
        [filteredBtnData addObject:dic];
    }
    
    CGFloat btnWidth = UI_SCREEN_WIDTH / filteredBtnData.count;
    CGFloat btnHeight = 90;
    for (NSInteger i = 0; i < filteredBtnData.count; i++) {
        NSDictionary *dic = filteredBtnData[i];
        FMUpDownButton *btn = [[FMUpDownButton alloc] initWithFrame:CGRectZero font:FontWithSize(14) normalTextColor:ColorWithHex(0x333333) backgroundColor:FMWhiteColor title:dic[@"title"] image:ImageWithName(dic[@"img"]) target:self action:@selector(btnClicked:)];
        [self.contentView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(sepLine.mas_bottom);
            make.left.equalTo(@(i * btnWidth));
            make.width.equalTo(@(btnWidth));
            make.height.equalTo(@(btnHeight));
        }];
        btn.imageToTextHeight = 6.0f;
        btn.tag = [self.btnDataArr indexOfObject:dic];
    }
}

- (void)show {
    [[UIApplication sharedApplication].keyWindow addSubview:self];
    self.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT);
    [UIView animateWithDuration:0.3 animations:^{
        self.bgView.alpha = 1.0;
        self.contentView.frame = CGRectMake(0, UI_SCREEN_HEIGHT-(kContentViewHeight+UI_SAFEAREA_BOTTOM_HEIGHT), UI_SCREEN_WIDTH, kContentViewHeight + UI_SAFEAREA_BOTTOM_HEIGHT);
    }];
}

- (void)setChoosedBrokerModel:(FMChooseBrokerModel *)choosedBrokerModel {
    _choosedBrokerModel = choosedBrokerModel;
    
    self.titleLabel.text = choosedBrokerModel.name;
}

- (void)chooseBroker {
    [self removeFromSuperview];

    FMChooseBrokerViewController *vc = [[FMChooseBrokerViewController alloc] init];
    vc.type = ChooseBrokerTypeSwitch;
    vc.choosedBrokerId = self.choosedBrokerModel.brokerId;
    vc.switchBlock = ^(FMChooseBrokerModel * _Nonnull choosedModel) {
        FMMarketTradePopView *popView = [[FMMarketTradePopView alloc] init];
        popView.choosedBrokerModel = choosedModel;
        popView.stockCode = self.stockCode;
        [popView show];
    };
    [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
}

- (void)close {
    [UIView animateWithDuration:0.3 animations:^{
        self.bgView.alpha = 0.0;
        self.contentView.frame = CGRectMake(0, UI_SCREEN_HEIGHT, UI_SCREEN_WIDTH, kContentViewHeight + UI_SAFEAREA_BOTTOM_HEIGHT);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)btnClicked:(UIButton *)btn {
    if (!self.choosedBrokerModel.ableExchange) {
        if (btn.tag < 3) {
            [SVProgressHUD showImage:nil status:@"暂未对接交易功能"];
            return;
        }
    }
    
    // 对于买卖操作，先获取实时价格
    if (btn.tag == 0 || btn.tag == 1) {
        [self fetchStockPriceAndExecuteAction:btn.tag];
        return;
    }
    
    [self removeFromSuperview];
    switch (btn.tag) {
        case 2:
            [ProtocolJump jumpWithUrl:self.choosedBrokerModel.holdUrl];
            break;
            
        case 3:
            if ([self.choosedBrokerModel.openUrl hasPrefix:@"qcyzt://"]) {
                [ProtocolJump jumpWithUrl:self.choosedBrokerModel.openUrl];
            } else {
                FMWKWebViewController *vc = [[FMWKWebViewController alloc] init];
                vc.urlStr = self.choosedBrokerModel.openUrl;
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            }
            break;
            
        default:
            break;
    }
}

- (NSArray *)btnDataArr {
    if (!_btnDataArr) {
        _btnDataArr = @[@{@"img" : @"stock_buy", @"title" : @"买入"},
                        @{@"img" : @"stock_sell", @"title" : @"卖出"},
                        @{@"img" : @"stock_hold", @"title" : @"持仓"},
                        @{@"img" : @"stock_openAccount", @"title" : @"开户"}];
    }
    
    return _btnDataArr;
}

#pragma mark - 获取股票实时价格并执行交易操作
- (void)fetchStockPriceAndExecuteAction:(NSInteger)actionType {
    if (!self.stockCode.length) {
        [self removeFromSuperview];
        return;
    }
    
    // 解析股票代码获取setCode和code
    UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:self.stockCode];
    if (!matchInfo) {
        [self removeFromSuperview];
        return;
    }
    
    // 创建行情请求
    UPMarketStockHqReq *hqReq = [[UPMarketStockHqReq alloc] initWithSetCode:matchInfo.setCode code:matchInfo.code];
        
    // 请求股票行情
    WeakSelf(weakSelf)
    [UPMarketManager requestStockHq:hqReq completionHandler:^(UPMarketStockHqRsp *rsp, NSError *error) {
        // 获取股票行情数据
        UPHqStockHq *stockHq = rsp.dataArray.firstObject;
        NSString *currentPrice = [NSString stringWithFormat:@"%.2f", stockHq.nowPrice];
        [weakSelf executeTradeActionWithType:actionType price:currentPrice];
    }];
}

- (void)executeTradeActionWithType:(NSInteger)actionType price:(NSString *)price {
    [self removeFromSuperview];
    
    NSString *urlString = @"";
    switch (actionType) {
        case 0: // 买入
            if (price.length) {
                urlString = [NSString stringWithFormat:@"%@&code=%@&price=%@", self.choosedBrokerModel.buyUrl, self.stockCode, price];
            } else {
                urlString = [NSString stringWithFormat:@"%@&code=%@", self.choosedBrokerModel.buyUrl, self.stockCode];
            }
            break;
            
        case 1: // 卖出
            if (price.length) {
                urlString = [NSString stringWithFormat:@"%@&code=%@&price=%@", self.choosedBrokerModel.saleUrl, self.stockCode, price];
            } else {
                urlString = [NSString stringWithFormat:@"%@&code=%@", self.choosedBrokerModel.saleUrl, self.stockCode];
            }
            break;
            
        default:
            break;
    }
    
    if (urlString.length) {
        [ProtocolJump jumpWithUrl:urlString];
    }
}

@end
