//
//  FMUserCenterViewController.m
//  QCYZT
//
//  Created by zeng on 2021/10/12.
//  Copyright © 2021 LZKJ. All rights reserved.
//

#import "FMUserCenterViewController.h"
#import "FMHomePageMsgButton.h"
#import "FMUserCenterTopCell.h"
#import "FMUserCenterVIPServiceCell.h"
#import "FMUserCenterMiddleCell.h"
#import "FMUserCenterMyServiceCell.h"
#import "FMUserCenterActivityCell.h"
#import "FMUserCenterMoreServiceCell.h"
#import "FMSystemSettingVC.h"
#import "FMMeModel.h"
#import "FMMyCollectionViewController.h"
#import "FMMeMyTrackViewController.h"
#import "ConsumeHistoryViewController.h"
#import "YTGNormalWebVC.h"
#import "FMAboutusViewController.h"
#import "FMActivityModel.h"
#import "FMMyStareStockConfigInfoVC.h"
#import "FMTaskConfigModel.h"
#import "FMUserCenterOftenVisitTabCell.h"
#import "FMServiceChooseBigcastViewController.h"
#import "FMPublishContentDraftListVC.h"
#import "FMShareHelper.h"
#import "FMMyFocusViewController.h"
#import "FMUserCenterMyStrategiesCell.h"
#import "HttpRequestTool+UserCenter.h"
#import "HttpRequestTool+Strategy.h"
#import "FMMonthlyTeachingSummaryViewController.h"

@interface FMUserCenterViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UIImageView *topBgImgV;
@property (nonatomic, strong) FMHomePageMsgButton *msgBtn;
@property (nonatomic, strong) UIButton *settingBtn;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *myServiceArr;
@property (nonatomic, strong) NSArray<FMStockStrategyMyStrategyModel *> *myStrategyArr;
@property (nonatomic, strong) NSArray *visitsArray;
@property (nonatomic, strong) NSArray *moreServiceArr;
@property (nonatomic, strong) NSArray *activityArr;

@end

@implementation FMUserCenterViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.view.backgroundColor = UIColor.up_contentBgColor;
        
    [self.view addSubview:self.topBgImgV];
    self.topBgImgV.frame = CGRectMake(0, 0, UI_SCREEN_WIDTH, 300);

    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.insets(UIEdgeInsetsMake(UI_SAFEAREA_TOP_HEIGHT, 0, 0, 0));
    }];
   
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(msgUnreadStatusJudge) name:kMsgUnreadNumChanged object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
    
    if (![UPThemeManager isDarkTheme]) {
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDefault;
    } else {
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
    }
    
    if ([FMHelper isLogined]) {
        [self getPersonalInfo];
        [self getOffenVisit];
        [self requestStrategies];
    } else {
        self.myStrategyArr = nil;
        self.visitsArray = nil;
        self.activityArr = nil;
    }
    
    [self msgUnreadStatusJudge];
    [self getActivityInfo];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
}

- (void)msgUnreadStatusJudge {
    if (![FMHelper isLogined]) {
        [self.msgBtn msgUnreadStatusChanged:0];
    } else {
        [self.msgBtn msgUnreadStatusChanged:[[FMUserDefault getSeting:@"msgUnread"] integerValue]];
    }
}

- (void)getOffenVisit {
    [HttpRequestTool getUserCenterOffenVisitRequestStart:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            if ([dic[@"data"] isKindOfClass:[NSArray class]]) {
                self.visitsArray = dic[@"data"];
                [self.tableView reloadData];
            }
        }
    }];
}

#pragma mark - UITableViewDelegate
-(NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 8;
}

-(NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return 1;
}

-(UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        FMUserCenterTopCell *cell = [tableView reuseCellClass:[FMUserCenterTopCell class]];
        [cell setData];
        return cell;
    } else if (indexPath.section == 1) {
        FMUserCenterVIPServiceCell *cell = [tableView reuseCellClass:[FMUserCenterVIPServiceCell class]];
        return cell;
    } else if (indexPath.section == 2) {
        if ([FMHelper isLogined]) {
            FMUserCenterMiddleCell *cell = [tableView reuseCellClass:[FMUserCenterMiddleCell class]];
            [cell setData];
            return cell;
        } else {
            return [tableView reuseCellClass:[UITableViewCell class]];
        }
    } else if (indexPath.section == 3) {
        FMUserCenterMyServiceCell *cell = [tableView reuseCellClass:[FMUserCenterMyServiceCell class]];
        cell.dataArr = [self.myServiceArr mutableCopy];
        return cell;
    }  else if (indexPath.section == 4) {
        if (!self.myStrategyArr.count) {
            return [tableView reuseCellClass:[UITableViewCell class]];
        }
        FMUserCenterMyStrategiesCell *cell = [tableView reuseCellClass:[FMUserCenterMyStrategiesCell class]];
        cell.dataArr = self.myStrategyArr;
        return cell;
    } else if (indexPath.section == 5) {
        if (!self.activityArr.count) {
            return [tableView reuseCellClass:[UITableViewCell class]];
        }
        FMUserCenterActivityCell *cell = [tableView reuseCellClass:[FMUserCenterActivityCell class]];
        cell.imgUrlArr = [self.activityArr valueForKeyPath:@"image"];
        cell.actionArr = [self.activityArr valueForKeyPath:@"action"];
        return cell;
    } else if (indexPath.section == 6) {
        if (!self.visitsArray.count) {
            return [tableView reuseCellClass:[UITableViewCell class]];
        }
        FMUserCenterOftenVisitTabCell *cell = [tableView reuseCellClass:[FMUserCenterOftenVisitTabCell class]];
        cell.activityArr = self.activityArr;
        cell.dataArr = self.visitsArray;
        return cell;
    }
    
    FMUserCenterMoreServiceCell *cell = [tableView reuseCellClass:[FMUserCenterMoreServiceCell class]];
    cell.dataArr = [self.moreServiceArr mutableCopy];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        return  [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMUserCenterTopCell class]) configuration:^(FMUserCenterTopCell *cell) {
            [cell setData];
        }];
    } else if (indexPath.section == 1) {
        if ([FMHelper isLogined]) {
            return (UI_SCREEN_WIDTH - 30) * 60 / 345.0;
        } else {
            return CGFLOAT_MIN;
        }
    } else if (indexPath.section == 2) {
        if ([FMHelper isLogined]) {
            return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([FMUserCenterMiddleCell class]) configuration:^(FMUserCenterMiddleCell *cell) {
                [cell setData];
            }];
        }
        return CGFLOAT_MIN;
    } else if (indexPath.section == 3) {
        if ([FMHelper isBigFont]) {
            return UI_Relative_WidthValue(70) * ((self.myServiceArr.count + 3) / 4) + 10 + 50;
        } else {
            return UI_Relative_WidthValue(70) * ((self.myServiceArr.count + 4) / 5) + 10 + 50;
        }
    } else if (indexPath.section == 4) {
        if (self.myStrategyArr.count) {
            return ceil(UI_SCREEN_WIDTH / 4) - 44 + 7 + 20 + 13 + 24 + 13 + 15;
        }
        return CGFLOAT_MIN;
    } else if (indexPath.section == 5) {
        if (self.activityArr.count == 0) {
            return CGFLOAT_MIN;
        }
        return UITableViewAutomaticDimension;
    } else if (indexPath.section == 6) {
        if (self.visitsArray.count == 0  || ![FMHelper isLogined]) {
            return  CGFLOAT_MIN;
        }
        CGFloat space = self.activityArr.count == 0 ? 10 : 0;
        return ceil(UI_SCREEN_WIDTH / 5) - 25 + 7 + 20 + 3 + 24 + 13 + 15 + space;
    }
    
    return UI_Relative_WidthValue(70) * ((self.moreServiceArr.count + 3) / 4) + 50;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *view = [UIView new];
    view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 3) {
        return 8.0f;
    } else if (section == 4) {
        if (self.myStrategyArr.count) {
            return 8.0f;
        }
    } else if (section == 6) {
        if (self.activityArr.count || self.visitsArray.count) {
            return 8;
        }
    }
    return CGFLOAT_MIN;
}

#pragma mark - HTTP
- (void)getPersonalInfo {
    NSString *token = [FMUserDefault getUserToken];
    if (!token.length) {
        [self.tableView reloadData];
        return;
    }
    if ([FMHelper isLogined]) { // 登录状态直接请求信息
        [self requestInfoFailDelay:3.0];
    } else {
        // 有token没有id，静默登录一下
        [HttpRequestTool userLoginAuthByToken:^{
            [self requestInfoFailDelay:1.0];
        } authTokenFailureBlock:^{
        }];
    }
}

- (void)getActivityInfo {
    [HttpRequestTool getEnableActivityListStart:^{
    } failure:^{
        self.activityArr = @[];
        [self.tableView reloadData];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray *arr = [NSArray modelArrayWithClass:[FMActivityModel class] json:dic[@"data"]];
            if (arr.count >= 3) {
                self.activityArr = [arr subarrayWithRange:NSMakeRange(0, 3)];
            } else {
                self.activityArr = arr;
            }
            [self.tableView reloadData];
        } else {
            self.activityArr = @[];
            [self.tableView reloadData];
        }
    }];
}

- (void)requestInfoFailDelay:(CGFloat)delay {
    WEAKSELF
    [HttpRequestTool getPersonalInfoStart:^{
    } failure:^{
        [__weakSelf performSelector:@selector(getPersonalInfo) withObject:nil afterDelay:delay];
    } success:^(NSDictionary *dic) {
        if([dic[@"status"] isEqualToString:@"1"] && [dic[@"data"] isKindOfClass:[NSDictionary class]]) {
            NSDictionary *personalInfo = dic[@"data"];
            NSDictionary *answerDic = [JsonTool dicOrArrFromJsonString:personalInfo[@"answer"]];
            FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
            userModel.answerFlag = [answerDic[@"answerResult"][@"level"] integerValue];
            userModel.answerDesc = answerDic[@"answerResult"][@"levelName"];
            userModel.riskEvaluationSuccessed = personalInfo[@"riskEvaluationSuccessed"];
            [userModel setValuesForKeysWithDictionary:personalInfo];
            if (userModel.registrationStatus == -1) {
                [PushMessageView showWithTitle:@"提示" message:userModel.registrationComplianceMsg noticeImage:nil sureTitle:@"确定" cancelTitle:nil clickSure:^{
                    [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://investorInfo?reason=%@", userModel.registrationComplianceMsg]];
                } clickCancel:nil];
            }
            [MyKeyChainManager save:kUserModel data:userModel];
            
            [__weakSelf.tableView reloadData];
        }
    }];
}

- (void)requestStrategies {
    [HttpRequestTool requestMyStrategyWithStart:^{
    } failure:^{
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMStockStrategyMyStrategyModel class] json:dic[@"data"]];
            // 去掉过期数据并去重
            NSMutableArray<FMStockStrategyMyStrategyModel *> *filteredArray = [NSMutableArray array];
            NSMutableSet<NSString *> *templateIdSet = [NSMutableSet set];
            for (FMStockStrategyMyStrategyModel *model in dataArr) {
                if (model.status < 3 && ![templateIdSet containsObject:model.templateId]) {
                    [filteredArray addObject:model];
                    [templateIdSet addObject:model.templateId];
                }
            }
            
            if (filteredArray.count <= 3) {
                self.myStrategyArr = filteredArray;
            } else {
                NSMutableArray *arr = [NSMutableArray arrayWithArray:[filteredArray subarrayWithRange:NSMakeRange(0, 3)]];
                FMStockStrategyMyStrategyModel *model = [FMStockStrategyMyStrategyModel new];
                model.name = @"查看更多";
                [arr addObject:model];
                self.myStrategyArr = arr.copy;
            }

            [self.tableView reloadData];
        }
    }];
}

- (void)messageDetail{
#if DEBUG
    FMMonthlyTeachingSummaryViewController *vc = [FMMonthlyTeachingSummaryViewController new];
    [self.navigationController pushViewController:vc animated:YES];
#else
    FMMessageViewController *vc = [[FMMessageViewController alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
#endif
}

- (void)jumpToSetting {
//#if DEBUG
//    TwoPageScrollViewDemoVC *vc = [TwoPageScrollViewDemoVC new];
//    [self.navigationController pushViewController:vc animated:YES];
//#else
    FMSystemSettingVC *vc = [[FMSystemSettingVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
//#endif
}

#pragma mark - Getter/Setter
- (UIImageView *)topBgImgV {
    if (!_topBgImgV) {
        NSString *bundlePath = [[NSBundle mainBundle] pathForResource:@"FMResources" ofType:@"bundle"];
        NSArray *files = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:bundlePath error:nil];
        NSLog(@"FMResources files: %@", files);
        _topBgImgV = [[UIImageView alloc] initWithImage:FMImgInBundle(@"我的/首页顶部背景")];
        _topBgImgV.contentMode = UIViewContentModeScaleAspectFill;
        _topBgImgV.userInteractionEnabled = YES;
        
        UIView *navView = [[UIView alloc] init];
        [_topBgImgV addSubview:navView];
        [navView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(@0);
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.height.equalTo(@(UI_NAVBAR_HEIGHT));
        }];
        navView.backgroundColor = FMClearColor;
        
        [navView addSubview:self.msgBtn];
        [self.msgBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(@-23);
            make.centerY.equalTo(@0);
        }];
        
        [navView addSubview:self.settingBtn];
        [self.settingBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.msgBtn.mas_left).offset(-15);
            make.centerY.equalTo(@0);
        }];
    }
    
    return _topBgImgV;
}

- (FMHomePageMsgButton *)msgBtn {
    if (!_msgBtn) {
        FMHomePageMsgButton *messageBtn = [FMHomePageMsgButton buttonWithType:UIButtonTypeCustom];
        [messageBtn setImage:FMImgInBundle(@"我的/通知") forState:UIControlStateNormal];
        [messageBtn addTarget:self action:@selector(messageDetail) forControlEvents:UIControlEventTouchUpInside];
        _msgBtn = messageBtn;
    }
    
    return _msgBtn;
}

- (UIButton *)settingBtn {
    if (!_settingBtn) {
        _settingBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_settingBtn setImage:FMImgInBundle(@"我的/设置") forState:UIControlStateNormal];
        _settingBtn.backgroundColor = [UIColor clearColor];
        _settingBtn.layer.borderWidth = 0;
        [_settingBtn addTarget:self action:@selector(jumpToSetting) forControlEvents:UIControlEventTouchUpInside];
    }
    return _settingBtn;
}

- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped delegate:self dataSource:self viewController:self];
        _tableView.estimatedRowHeight = 100;
        [_tableView registerCellClass:[FMUserCenterTopCell class]];
        [_tableView registerCellClass:[FMUserCenterVIPServiceCell class]];
        [_tableView registerCellClass:[FMUserCenterMiddleCell class]];
        [_tableView registerCellClass:[FMUserCenterMyServiceCell class]];
        [_tableView registerCellClass:[FMUserCenterActivityCell class]];
        [_tableView registerCellClass:[FMUserCenterMoreServiceCell class]];
        [_tableView registerCellClass:[FMUserCenterOftenVisitTabCell class]];
        [_tableView registerCellClass:[FMUserCenterMyStrategiesCell class]];
        [_tableView registerCellClass:[UITableViewCell class]];
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.backgroundColor = FMClearColor;
        _tableView.showsVerticalScrollIndicator = NO;
    }
    
    return _tableView;
}

- (NSArray *)myServiceArr {
    FMMeModel *tgbmx = [FMMeModel modelWithIcon:@"已购服务" title:@"已购服务"];
    tgbmx.needLogin = YES;
    tgbmx.optionBlock = ^{
        [ProtocolJump jumpWithUrl:@"qcyzt://purchasedService"];
    };
    FMMeModel *draft = [FMMeModel modelWithIcon:@"草稿箱" title:@"草稿箱"];
    draft.needLogin = YES;
    draft.optionBlock = ^{
        [FMHelper checkLoginStatusNeedJumpToLoginWithBackBlock:^(FMUserModel *userInfoModel) {
            [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
                YTGNormalWebVC *vc = [[YTGNormalWebVC alloc] init];
                vc.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_Draft_List];
                [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
            }];
        }];
    };
    
    FMMeModel *wdsc = [FMMeModel modelWithIcon:@"我的收藏" title:@"我的收藏"];
    wdsc.needLogin = YES;
    wdsc.optionBlock = ^{
        FMMyCollectionViewController *collectVC = [[FMMyCollectionViewController alloc] init];
        collectVC.index = 0;
        [[FMHelper getCurrentVC].navigationController pushViewController:collectVC animated:YES];
    };
    FMMeModel *wdzj = [FMMeModel modelWithIcon:@"我的足迹" title:@"我的足迹"];
    wdzj.needLogin = YES;
    wdzj.optionBlock = ^{
        FMMeMyTrackViewController *trackVC = [[FMMeMyTrackViewController alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:trackVC animated:YES];
    };
    FMMeModel *flzx = [FMMeModel modelWithIcon:@"福利中心" title:@"福利中心"];
    flzx.needLogin = YES;
    flzx.optionBlock = ^{
        [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
            [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://web?url=%@%@&title=%@", prefix.URLEncodedString, kAPI_UserCenter_FLZX.URLEncodedString, @"福利中心".URLEncodedString]];
        }];
    };
    
    FMMeModel *wdgz = [FMMeModel modelWithIcon:@"我的关注" title:@"我的关注"];
    wdgz.needLogin = YES;
    wdgz.optionBlock = ^{
        // 关注
        FMMyFocusViewController *vc = [[FMMyFocusViewController alloc] init];
        [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
    };
    if([FMUserDefault getUserFlag] == 1) {
        _myServiceArr = @[wdsc, tgbmx, wdzj, draft, flzx];
    } else {
        _myServiceArr = @[wdgz, wdsc, tgbmx, wdzj, flzx];
    }
    
    return _myServiceArr;
}


- (NSArray *)moreServiceArr {
    WEAKSELF
    FMMeModel *jbmx = [FMMeModel modelWithIcon:@"金币明细" title:@"收支明细"];
    jbmx.needLogin = YES;
    jbmx.optionBlock = ^{
        ConsumeHistoryViewController *vc = [[ConsumeHistoryViewController alloc] init];
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    };
    
    FMMeModel *wdyj = [FMMeModel modelWithIcon:@"我的预警" title:@"我的预警"];
    wdyj.needLogin = YES;
    wdyj.optionBlock = ^{
        FMMyStareStockConfigInfoVC *vc = [[FMMyStareStockConfigInfoVC alloc] init];
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    };
    
    FMMeModel *xxdj = [FMMeModel modelWithIcon:@"信息登记" title:@"信息登记"];
    xxdj.needLogin = YES;
    xxdj.optionBlock = ^{
        [ProtocolJump jumpWithUrl:@"qcyzt://investorInfo"];
    };
    
    FMMeModel *fxcp = [FMMeModel modelWithIcon:@"风险测评" title:@"风险测评"];
    fxcp.needLogin = YES;
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    if ([FMHelper isLogined] && !userModel.riskEvaluationSuccessed) { 
        FMTaskConfigModel *taskConfig = [FMUserDefault getUnArchiverDataForKey:AppInit_Task];
        NSInteger awardType = taskConfig.taskDic[@(FMTaskTypeRiskEvaluation)].awardType;
        NSInteger awardValue = taskConfig.taskDic[@(FMTaskTypeRiskEvaluation)].awardValue;
        if (awardValue == 0) {
            fxcp.subTitle = [[NSAttributedString alloc] initWithString:@""];
        } else {
            if (awardType == 1) {
                fxcp.subTitle = [[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"奖励%zd积分", awardValue]];
            } else if (awardType == 2) {
                fxcp.subTitle = [[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"可得%zd次抽奖", awardValue]];
            } else if (awardType == 3) {
                fxcp.subTitle =[[NSAttributedString alloc] initWithString:@"可得笔记券"];
            } else if (awardType == 4) {
                fxcp.subTitle =[[NSAttributedString alloc] initWithString:@"可得私信券"];
            } else if (awardType == 5) {
                fxcp.subTitle =[[NSAttributedString alloc] initWithString:@"可得问股券"];
            } else {
                fxcp.subTitle = [[NSAttributedString alloc] initWithString:@""];
            }
        }
    } else {
        fxcp.subTitle = [[NSAttributedString alloc] initWithString:@""];
    }
    fxcp.optionBlock = ^{
        [ProtocolJump jumpWithUrl:@"qcyzt://riskeValuation"];
    };
    
    FMMeModel *tggs = [FMMeModel modelWithIcon:@"从业资质" title:@"从业资质"];
    tggs.optionBlock = ^{
        [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
            YTGNormalWebVC *webVC = [[YTGNormalWebVC alloc] init];
            webVC.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_UserCenter_TGZZGS];
            webVC.titleStr = @"从业资质公示";
            [__weakSelf.navigationController pushViewController:webVC animated:YES];
        }];
    };
    
    FMMeModel *cjwt = [FMMeModel modelWithIcon:@"常见问题" title:@"常见问题"];
    cjwt.optionBlock = ^{
        [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
            YTGNormalWebVC *vc = [[YTGNormalWebVC alloc] init];
            vc.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_UserCenter_Problem];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    };
    
    FMMeModel *gywm = [FMMeModel modelWithIcon:@"关于我们" title:@"关于我们"];
    gywm.optionBlock = ^{
        FMAboutusViewController *vc = [[FMAboutusViewController alloc] init];
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    };
    
    FMMeModel *lxkf = [FMMeModel modelWithIcon:@"联系客服" title:@"联系客服"];
    lxkf.optionBlock = ^{
        [FMHelper showCustomService];
    };
    
    FMMeModel *lxyl = [FMMeModel modelWithIcon:@"拉新有礼" title:@"拉新有礼"];
    lxyl.needLogin = YES;
    lxyl.optionBlock = ^{
        [ProtocolJump jumpWithUrl:@"qcyzt://gotoshare"];
    };
    
    FMMeModel *lqtq = [FMMeModel modelWithIcon:@"领取特权" title:@"领取特权"];
    lqtq.needLogin = YES;
    lqtq.optionBlock = ^{
        FMServiceChooseBigcastViewController *vc = [[FMServiceChooseBigcastViewController alloc] init];
        vc.type = ServiceChooseTypeVipBindingBigcastList;
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    };
    
    FMMeModel *dhm = [FMMeModel modelWithIcon:@"兑换码" title:@"兑换码"];
    dhm.needLogin = YES;
    dhm.optionBlock = ^{
        [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
            YTGNormalWebVC *vc = [[YTGNormalWebVC alloc] init];
            vc.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_UserCenter_DHM];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    };
    
    FMMeModel *jump = [FMMeModel modelWithIcon:@"" title:@"跳转协议"];
    jump.optionBlock = ^{
        [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
            YTGNormalWebVC *vc = [[YTGNormalWebVC alloc] init];
            [[FMHelper getCurrentVC].navigationController pushViewController:vc animated:YES];
        }];
    };
    
    NSMutableArray *arr = [NSMutableArray array];
    [arr addObjectsFromArray:@[jbmx, xxdj, fxcp, wdyj, tggs, cjwt, gywm, lxkf, lxyl, dhm]];
    if (userModel.vip) {
        [arr insertObject:lqtq atIndex:9];
    }
#if DEBUG
    [arr addObject:jump];
#endif

    _moreServiceArr = arr.copy;
    return _moreServiceArr;
}


@end
