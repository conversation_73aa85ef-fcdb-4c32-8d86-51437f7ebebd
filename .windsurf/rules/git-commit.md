---
trigger: model_decision
description: git提交规范
---

# Git 规范

## 提交规范
git 提交记录样例： [type]：[description]。

* 以下是 type 的枚举值：
- feat： 新增功能
- fix：修复 bug
- docs：文档注释
- style： 代码格式（不影响代码运行的变动）
- refactor：重构、优化（既不增加新功能，也不是修复bug）
- perf： 性能优化
- test： 增加测试
- chore： 构建过程或辅助工具的变动
- revert： 回退
- build： 打包

* description 使用中文，要高度精简提炼主要内容

* 示例
- docs：更新NetworkMananger.m注释
  fix：YYLabel在暗黑模式下高亮显示异常问题

# 分支管理
- main/master： 主分支，保持稳定可发布状态
- develop： 开发分支，包含最新开发特性
- feature/*：功能分支，用于开发新功能
- bugfix/*：修复分支，用于修复bug
- release/*：发布分支，用于准备发布

# 重要原则
- **重要**：不要自动提交git 代码，除非有明确的提示
- 提交前确保代码通过所有测试
- 保持提交信息简洁明了，描述清楚变更内容
- 避免大型提交，尽量将变更分解为小的、相关的提交

# 开发工作流程

## 功能开发流程
1. **创建功能分支**：从 develop 分支创建 feature/功能名称 分支
2. **开发和测试**：在功能分支上进行开发，确保代码质量
3. **提交代码**：按照提交规范提交代码，每个提交应该是一个完整的功能点
4. **合并请求**：创建 Pull Request，请求合并到 develop 分支
5. **代码审查**：团队成员进行代码审查，确保代码质量
6. **合并代码**：审查通过后合并到 develop 分支

## 提交最佳实践
- **原子性提交**：每次提交应该是一个完整的、可工作的功能点
- **清晰的提交信息**：提交信息应该清楚描述做了什么改动
- **避免大型提交**：将大的改动分解为多个小的、相关的提交
- **提交前检查**：确保代码编译通过，测试用例通过

## 冲突处理
- **及时同步**：定期从主分支拉取最新代码
- **小步快跑**：频繁提交，减少冲突的可能性
- **谨慎合并**：遇到冲突时仔细检查，确保不丢失重要代码
