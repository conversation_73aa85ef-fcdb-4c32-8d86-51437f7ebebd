---
trigger: always_on
---

# 开发指导原则

> 本文档是项目开发的总体指导原则，具体的技术规范请参考对应的专项规范文档：
> - [代码风格规范](./code_style.md)
> - [API开发规范](./api_guidelines.md)
> - [UI开发规范](./ui_guidelines.md)
> - [项目结构规范](./project_structure.md)
> - [Git使用规范](./git.md)
> - [测试规范](./testing_guidelines.md)

## 核心原则

### 信息收集优先原则
- **在进行任何代码修改前，必须先收集相关信息**
- 询问具体、详细的问题以获得准确的代码上下文
- 了解相关类、方法、属性的完整信息后再进行修改

### 渐进式开发原则
- 将复杂任务分解为小的、可管理的步骤
- 每次只专注于一个具体的功能点
- 在完成一个步骤后再进行下一步

## 代码分析最佳实践

### 查询策略
```
// ✅ 好的查询示例
"HttpRequestTool类的所有网络请求方法，包括GET、POST、PUT等方法的具体实现"
"MemberCenter模块中所有与VIP功能相关的类和方法"
"项目中所有自定义UILabel初始化方法和字体设置相关的分类方法"

// ❌ 避免的查询方式
"网络请求"  // 太宽泛
"按钮"      // 不够具体
"登录"      // 缺少上下文
```

### 查询时机
1. **开始新功能开发前** - 了解相关模块的现有实现
2. **修改现有代码前** - 获取完整的类和方法信息
3. **遇到问题时** - 搜索相似的解决方案
4. **集成第三方库前** - 了解项目现有的类似功能

### 查询深度要求
- 不仅要查询目标类，还要查询其依赖的类
- 包含方法的参数、返回值、实现细节
- 了解相关的分类扩展和工具方法
- 查询使用示例和调用方式

## 分析流程

### 整体分析流程
1. **整体架构理解** - 先了解模块的整体结构
2. **关键类识别** - 找出核心的类和接口
3. **依赖关系分析** - 理解类之间的依赖关系
4. **实现细节研究** - 深入了解具体实现

### 分析重点
- 网络请求的封装和使用方式
- UI组件的创建和配置模式
- 数据模型的定义和转换
- 工具类和分类的使用规范

## 任务管理规范

### 复杂任务分解
- 将大任务分解为小任务
- 每个子任务应该是一个独立的、可完成的工作单元
- 任务之间应该有清晰的依赖关系

### 任务状态管理
- `NOT_STARTED` - 尚未开始的任务
- `IN_PROGRESS` - 正在进行的任务
- `COMPLETE` - 已完成的任务
- `CANCELLED` - 已取消的任务

### 任务更新原则
- 开始新任务时，同时更新前一个任务为完成状态
- 及时反映任务进度和状态变化
- 保持任务列表的准确性

## 代码修改规范

### 修改前准备
1. 获取完整的相关信息
2. 理解现有代码的设计模式和约定
3. 确认修改不会破坏现有功能
4. 了解相关的测试和验证方法

### 修改策略
- 优先使用现有的工具类和分类方法
- 遵循项目的命名和编码规范
- 保持与现有代码风格的一致性
- 避免重复实现已有功能

### 修改验证
- 修改后建议编写或更新测试
- 验证修改是否符合项目规范
- 确认没有引入新的问题或冲突

## 文件操作最佳实践

### 文件查看和编辑
- 查看文件内容时使用合适的范围和搜索
- 进行精确修改而非大范围替换
- 避免删除整个文件重新创建

### 搜索和导航
- 使用正则表达式进行精确搜索
- 利用文件路径模式快速定位
- 结合项目结构规范进行导航

### 包管理
- 优先使用包管理器而非手动编辑配置文件
- 了解项目的依赖管理策略
- 避免引入不必要的依赖

## 错误处理和调试

### 常见问题排查
1. **编译错误** - 检查导入语句和方法签名
2. **运行时错误** - 验证对象初始化和方法调用
3. **逻辑错误** - 对比现有实现找出差异

### 调试策略
- 参考相似功能的实现方式
- 逐步验证每个修改点
- 利用项目现有的调试工具

## 协作和沟通

### 与用户沟通
- 在进行重大修改前征求用户确认
- 清晰说明修改的目的和影响
- 提供多个解决方案供用户选择

### 文档和注释
- 为新增的复杂功能添加注释
- 更新相关的文档和说明
- 保持代码的可读性和可维护性

## 持续改进

### 学习和适应
- 持续学习项目的最佳实践
- 适应项目的发展和变化
- 总结经验并应用到后续开发中

### 质量保证
- 遵循项目的质量标准
- 进行充分的测试和验证
- 关注代码的性能和安全性
